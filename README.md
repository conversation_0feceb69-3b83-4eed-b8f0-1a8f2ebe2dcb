# Micro-Core

下一代微前端架构解决方案

## 特性

- 🎯 **微内核架构** - 插件化设计，核心功能模块化
- 🛡️ **多层沙箱隔离** - 支持多种沙箱策略，确保应用隔离
- 🔧 **跨框架集成** - 支持React、Vue、Angular等主流框架
- 🚀 **渐进式迁移** - 零配置Sidecar模式，一行代码接入
- 📦 **TypeScript优先** - 完整的类型支持和开发体验

## 快速开始

### 安装

```bash
pnpm add @micro-core/core
```

### 基础使用

```typescript
import { MicroCore } from '@micro-core/core';

const microCore = new MicroCore();

// 注册应用
await microCore.registerApp({
    name: 'my-app',
    entry: 'http://localhost:3000',
    container: '#app',
    activeWhen: '/my-app'
});

// 启动
await microCore.start();
```

### Sidecar模式

```typescript
import { sidecar } from '@micro-core/sidecar';

// 一行代码接入
sidecar.mount('#app');
```

## 包结构

- `@micro-core/core` - 核心运行时
- `@micro-core/shared` - 共享工具包
- `@micro-core/plugins` - 插件系统
- `@micro-core/adapters` - 框架适配器
- `@micro-core/builders` - 构建工具适配
- `@micro-core/sidecar` - 边车模式

## 开发

```bash
# 安装依赖
pnpm install

# 构建所有包
pnpm run build

# 开发模式
pnpm run dev

# 运行测试
pnpm run test

# 类型检查
pnpm run type-check
```

## 文档

详细文档请访问: [https://micro-core.dev](https://micro-core.dev)

## 许可证

MIT

## 贡献

欢迎提交Issue和Pull Request！
