{"$schema": "https://turbo.build/schema.json", "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**"], "env": ["NODE_ENV"]}, "dev": {"cache": false, "persistent": true}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {"outputs": []}, "clean": {"cache": false}}, "globalDependencies": ["package.json", "tsconfig.json", "turbo.json"]}