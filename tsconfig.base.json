{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@micro-core/shared": ["./packages/shared/src"], "@micro-core/shared/*": ["./packages/shared/src/*"], "@micro-core/core": ["./packages/core/src"], "@micro-core/core/*": ["./packages/core/src/*"], "@micro-core/plugins": ["./packages/plugins/src"], "@micro-core/plugins/*": ["./packages/plugins/src/*"]}}, "include": ["packages/*/src/**/*", "packages/*/types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/test/**", "**/tests/**", "**/__tests__/**"]}