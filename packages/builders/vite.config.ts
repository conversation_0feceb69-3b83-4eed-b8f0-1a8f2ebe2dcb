import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        dts({
            insertTypesEntry: true,
            rollupTypes: true
        })
    ],
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'Builders',
            formats: ['es', 'cjs'],
            fileName: (format) => `index.${format === 'es' ? 'mjs' : 'js'}`
        },
        rollupOptions: {
            external: [
                'react',
                'react-dom',
                'vue',
                '@vue/runtime-core',
                'angular'
            ],
            output: {
                globals: {
                    'react': 'React',
                    'react-dom': 'ReactDOM',
                    'vue': 'Vue'
                }
            }
        },
        sourcemap: true,
        minify: 'esbuild'
    },
    define: {
        __VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0')
    }
});
