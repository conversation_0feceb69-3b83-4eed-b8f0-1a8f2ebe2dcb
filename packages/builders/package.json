{"name": "@micro-core/builders", "version": "0.1.0", "description": "微前端构建工具适配器集合 - 支持Webpack、Vite、Rollup等主流构建工具", "keywords": ["micro-frontend", "microfrontend", "webpack", "vite", "rollup", "builder", "bundler"], "author": {"name": "Echo", "email": "<EMAIL>"}, "homepage": "https://micro-core.dev", "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md", "CHANGELOG.md"], "scripts": {"clean": "rimraf dist coverage test-results", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:unit": "vitest run --config vitest.config.ts", "test:integration": "vitest run --config vitest.config.ts --testNamePattern=integration", "test:ci": "node scripts/test-runner.js", "test:all": "pnpm run test:unit && pnpm run test:integration", "validate": "pnpm run type-check && pnpm run lint && pnpm run test:coverage", "ci": "pnpm run clean && pnpm run validate && pnpm run build", "prepublishOnly": "pnpm run ci", "build": "vite build", "dev": "vite build --watch", "preview": "vite preview"}, "dependencies": {"@micro-core/shared": "workspace:*", "@micro-core/core": "workspace:*"}, "peerDependencies": {"webpack": ">=5.0.0", "vite": ">=4.0.0", "rollup": ">=3.0.0", "esbuild": ">=0.17.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "vite": {"optional": true}, "rollup": {"optional": true}, "esbuild": {"optional": true}}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.3.3", "vitest": "^3.2.4", "eslint": "^8.57.0", "rimraf": "^5.0.0", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/builders"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}