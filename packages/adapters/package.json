{"name": "@micro-core/adapters", "version": "0.1.0", "description": "Micro-Core adapters 包", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest"}, "keywords": ["micro-frontend", "micro-core", "adapters"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters"}, "homepage": "https://micro-core.dev", "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "dependencies": {}, "devDependencies": {"typescript": "^5.3.3", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0", "vitest": "^3.2.4"}, "peerDependencies": {}}