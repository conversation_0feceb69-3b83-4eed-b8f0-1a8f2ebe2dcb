{"name": "@micro-core/sidecar", "version": "0.1.0", "description": "微前端边车模式支持 - 提供应用隔离、资源代理、通信桥接等核心功能", "keywords": ["microfrontend", "sidecar", "proxy", "isolation", "bridge"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md", "CHANGELOG.md"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "build": "vite build", "dev": "vite build --watch", "preview": "vite preview"}, "dependencies": {"@micro-core/shared": "workspace:*", "@micro-core/core": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.3.3", "vitest": "^3.2.4", "rimraf": "^5.0.0", "eslint": "^8.57.0", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0"}, "peerDependencies": {"typescript": ">=4.5.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/sidecar"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "homepage": "https://micro-core.dev"}