/**
 * @micro-core/plugins
 * Micro-Core 插件系统核心
 * 
 * @description 提供插件管理、加载和生命周期管理功能
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { PluginManager as IPluginManager, Plugin, PluginContext, PluginOptions } from '@micro-core/shared/types';

/**
 * 插件管理器
 * @description 负责插件的注册、安装、卸载和生命周期管理
 */
export class PluginManager implements IPluginManager {
    private logger = createLogger('PluginManager');
    private plugins = new Map<string, Plugin>();
    private installedPlugins = new Set<string>();
    private pluginContexts = new Map<string, PluginContext>();
    private kernel: any; // MicroCoreKernel

    constructor(kernel: any) {
        this.kernel = kernel;
    }

    /**
     * 注册插件
     * @param plugin 插件实例
     * @param options 插件选项
     */
    register(plugin: Plugin, options?: PluginOptions): void {
        if (!plugin.name) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_REGISTER_FAILED,
                '插件必须有名称',
                undefined,
                { plugin }
            );
        }

        if (this.plugins.has(plugin.name)) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_REGISTER_FAILED,
                `插件 "${plugin.name}" 已存在`,
                undefined,
                { pluginName: plugin.name }
            );
        }

        this.logger.info(`注册插件: ${plugin.name} v${plugin.version || '未知'}`);

        // 验证插件
        this.validatePlugin(plugin);

        // 注册插件
        this.plugins.set(plugin.name, plugin);

        // 创建插件上下文
        const context: PluginContext = {
            name: plugin.name,
            version: plugin.version || '0.0.0',
            options: options || {},
            registeredAt: Date.now(),
            installed: false
        };

        this.pluginContexts.set(plugin.name, context);

        this.logger.info(`插件注册成功: ${plugin.name}`);
    }

    /**
     * 安装插件
     * @param name 插件名称
     */
    async install(name: string): Promise<void> {
        const plugin = this.plugins.get(name);
        if (!plugin) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_INSTALL_FAILED,
                `插件 "${name}" 未注册`,
                undefined,
                { pluginName: name }
            );
        }

        if (this.installedPlugins.has(name)) {
            this.logger.warn(`插件 "${name}" 已安装`);
            return;
        }

        this.logger.info(`安装插件: ${name}`);

        try {
            // 检查依赖
            await this.checkDependencies(plugin);

            // 执行安装前钩子
            if (plugin.beforeInstall) {
                await plugin.beforeInstall(this.kernel);
            }

            // 安装插件
            if (plugin.install) {
                await plugin.install(this.kernel);
            }

            // 标记为已安装
            this.installedPlugins.add(name);

            // 更新上下文
            const context = this.pluginContexts.get(name);
            if (context) {
                context.installed = true;
                context.installedAt = Date.now();
            }

            // 执行安装后钩子
            if (plugin.afterInstall) {
                await plugin.afterInstall(this.kernel);
            }

            this.logger.info(`插件安装成功: ${name}`);
        } catch (error) {
            this.logger.error(`插件安装失败: ${name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_INSTALL_FAILED,
                `插件 "${name}" 安装失败: ${(error as Error).message}`,
                undefined,
                { pluginName: name },
                error as Error
            );
        }
    }

    /**
     * 卸载插件
     * @param name 插件名称
     */
    async uninstall(name: string): Promise<void> {
        const plugin = this.plugins.get(name);
        if (!plugin) {
            this.logger.warn(`插件 "${name}" 未注册`);
            return;
        }

        if (!this.installedPlugins.has(name)) {
            this.logger.warn(`插件 "${name}" 未安装`);
            return;
        }

        this.logger.info(`卸载插件: ${name}`);

        try {
            // 执行卸载前钩子
            if (plugin.beforeUninstall) {
                await plugin.beforeUninstall(this.kernel);
            }

            // 卸载插件
            if (plugin.uninstall) {
                await plugin.uninstall(this.kernel);
            }

            // 移除安装标记
            this.installedPlugins.delete(name);

            // 更新上下文
            const context = this.pluginContexts.get(name);
            if (context) {
                context.installed = false;
                context.uninstalledAt = Date.now();
            }

            // 执行卸载后钩子
            if (plugin.afterUninstall) {
                await plugin.afterUninstall(this.kernel);
            }

            this.logger.info(`插件卸载成功: ${name}`);
        } catch (error) {
            this.logger.error(`插件卸载失败: ${name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_UNINSTALL_FAILED,
                `插件 "${name}" 卸载失败: ${(error as Error).message}`,
                undefined,
                { pluginName: name },
                error as Error
            );
        }
    }

    /**
     * 获取插件
     * @param name 插件名称
     */
    get(name: string): Plugin | undefined {
        return this.plugins.get(name);
    }

    /**
     * 检查插件是否已注册
     * @param name 插件名称
     */
    has(name: string): boolean {
        return this.plugins.has(name);
    }

    /**
     * 检查插件是否已安装
     * @param name 插件名称
     */
    isInstalled(name: string): boolean {
        return this.installedPlugins.has(name);
    }

    /**
     * 获取所有插件名称
     */
    getPluginNames(): string[] {
        return Array.from(this.plugins.keys());
    }

    /**
     * 获取已安装的插件名称
     */
    getInstalledPluginNames(): string[] {
        return Array.from(this.installedPlugins);
    }

    /**
     * 获取插件上下文
     * @param name 插件名称
     */
    getPluginContext(name: string): PluginContext | undefined {
        return this.pluginContexts.get(name);
    }

    /**
     * 批量安装插件
     * @param names 插件名称数组
     */
    async installAll(names: string[]): Promise<void> {
        this.logger.info(`批量安装插件: ${names.join(', ')}`);

        const errors: Error[] = [];

        for (const name of names) {
            try {
                await this.install(name);
            } catch (error) {
                errors.push(error as Error);
            }
        }

        if (errors.length > 0) {
            this.logger.warn(`批量安装完成，失败: ${errors.length}个`);
        } else {
            this.logger.info(`批量安装成功: ${names.length}个插件`);
        }
    }

    /**
     * 批量卸载插件
     * @param names 插件名称数组
     */
    async uninstallAll(names: string[]): Promise<void> {
        this.logger.info(`批量卸载插件: ${names.join(', ')}`);

        const errors: Error[] = [];

        // 按相反顺序卸载
        for (const name of names.reverse()) {
            try {
                await this.uninstall(name);
            } catch (error) {
                errors.push(error as Error);
            }
        }

        if (errors.length > 0) {
            this.logger.warn(`批量卸载完成，失败: ${errors.length}个`);
        } else {
            this.logger.info(`批量卸载成功: ${names.length}个插件`);
        }
    }

    /**
     * 销毁插件管理器
     */
    async destroy(): Promise<void> {
        this.logger.info('销毁插件管理器');

        // 卸载所有已安装的插件
        const installedNames = Array.from(this.installedPlugins);
        await this.uninstallAll(installedNames);

        // 清理资源
        this.plugins.clear();
        this.installedPlugins.clear();
        this.pluginContexts.clear();

        this.logger.info('插件管理器销毁完成');
    }

    /**
     * 验证插件
     * @param plugin 插件实例
     */
    private validatePlugin(plugin: Plugin): void {
        if (!plugin.name || typeof plugin.name !== 'string') {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_REGISTER_FAILED,
                '插件名称必须是非空字符串',
                undefined,
                { plugin }
            );
        }

        if (plugin.install && typeof plugin.install !== 'function') {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_REGISTER_FAILED,
                '插件的install方法必须是函数',
                undefined,
                { pluginName: plugin.name }
            );
        }

        if (plugin.uninstall && typeof plugin.uninstall !== 'function') {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_REGISTER_FAILED,
                '插件的uninstall方法必须是函数',
                undefined,
                { pluginName: plugin.name }
            );
        }
    }

    /**
     * 检查插件依赖
     * @param plugin 插件实例
     */
    private async checkDependencies(plugin: Plugin): Promise<void> {
        if (!plugin.dependencies || plugin.dependencies.length === 0) {
            return;
        }

        const missingDeps: string[] = [];

        for (const dep of plugin.dependencies) {
            if (!this.installedPlugins.has(dep)) {
                missingDeps.push(dep);
            }
        }

        if (missingDeps.length > 0) {
            throw new MicroCoreError(
                ERROR_CODES.PLUGIN_DEPENDENCY_MISSING,
                `插件 "${plugin.name}" 缺少依赖: ${missingDeps.join(', ')}`,
                undefined,
                { pluginName: plugin.name, missingDependencies: missingDeps }
            );
        }
    }

    /**
     * 获取管理器状态
     */
    getStatus(): {
        totalPlugins: number;
        installedPlugins: number;
        pluginList: Array<{
            name: string;
            version: string;
            installed: boolean;
            registeredAt: number;
            installedAt?: number;
        }>;
    } {
        const pluginList = Array.from(this.plugins.entries()).map(([name, plugin]) => {
            const context = this.pluginContexts.get(name);
            return {
                name,
                version: plugin.version || '0.0.0',
                installed: this.installedPlugins.has(name),
                registeredAt: context?.registeredAt || 0,
                installedAt: context?.installedAt
            };
        });

        return {
            totalPlugins: this.plugins.size,
            installedPlugins: this.installedPlugins.size,
            pluginList
        };
    }
}

/**
 * 创建插件管理器
 * @param kernel 微前端内核
 */
export function createPluginManager(kernel: any): PluginManager {
    return new PluginManager(kernel);
}

// 导出插件相关类型
export type { Plugin, PluginContext, PluginOptions } from '@micro-core/shared/types';

// 版本信息
export const VERSION = '0.1.0';

// 默认导出
export default {
    PluginManager,
    createPluginManager,
    VERSION
};