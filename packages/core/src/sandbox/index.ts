/**
 * @fileoverview 沙箱系统导出
 * @description 导出所有沙箱相关的类、接口和工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 基础沙箱
export { BaseSandbox } from './base-sandbox';

// 具体沙箱实现
export { IframeSandbox } from './iframe-sandbox';
export { ProxySandbox } from './proxy-sandbox';
export { WebComponentSandbox } from './webcomponent-sandbox';

// 沙箱工厂和管理器
export { SandboxFactory } from './sandbox-factory';
export { SandboxManager } from './sandbox-manager';
export type { SandboxStats } from './sandbox-manager';

// 从shared模块重新导出类型
export type {
    SandboxContext, SandboxInstance, SandboxOptions, SandboxType
} from '@micro-core/shared/types';

// 从shared模块重新导出常量
export { SANDBOX_TYPES } from '@micro-core/shared/constants';
