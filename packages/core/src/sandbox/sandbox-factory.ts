/**
 * @fileoverview 沙箱工厂
 * @description 负责创建不同类型的沙箱实例
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError, SANDBOX_TYPES } from '@micro-core/shared';
import type { SandboxInstance, SandboxOptions, SandboxType } from '@micro-core/shared/types';
import { BaseSandbox } from './base-sandbox';
import { IframeSandbox } from './iframe-sandbox';
import { ProxySandbox } from './proxy-sandbox';
import { WebComponentSandbox } from './webcomponent-sandbox';

/**
 * 沙箱工厂类
 * @description 根据配置创建不同类型的沙箱实例
 */
export class SandboxFactory {
    private static logger = createLogger('SandboxFactory');
    private static sandboxClasses = new Map<SandboxType, typeof BaseSandbox>();

    static {
        // 注册内置沙箱类型
        this.registerSandboxType(SANDBOX_TYPES.PROXY, ProxySandbox);
        this.registerSandboxType(SANDBOX_TYPES.IFRAME, IframeSandbox);
        this.registerSandboxType(SANDBOX_TYPES.WEB_COMPONENT, WebComponentSandbox);
    }

    /**
     * 创建沙箱实例
     * @param options 沙箱配置选项
     * @returns 沙箱实例
     */
    static createSandbox(options: SandboxOptions): SandboxInstance {
        const { type = SANDBOX_TYPES.PROXY } = options;

        this.logger.info(`创建沙箱: ${options.name}, 类型: ${type}`);

        // 验证沙箱选项
        this.validateSandboxOptions(options);

        // 获取沙箱类
        const SandboxClass = this.sandboxClasses.get(type);
        if (!SandboxClass) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_TYPE_UNSUPPORTED,
                `不支持的沙箱类型: ${type}`,
                undefined,
                { sandboxType: type, availableTypes: Array.from(this.sandboxClasses.keys()) }
            );
        }

        try {
            // 创建沙箱实例
            const sandbox = new SandboxClass(options);
            this.logger.info(`沙箱创建成功: ${options.name}`);
            return sandbox;
        } catch (error) {
            this.logger.error(`沙箱创建失败: ${options.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                `沙箱创建失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: options.name, sandboxType: type },
                error as Error
            );
        }
    }

    /**
     * 注册沙箱类型
     * @param type 沙箱类型
     * @param SandboxClass 沙箱类
     */
    static registerSandboxType(type: SandboxType, SandboxClass: typeof BaseSandbox): void {
        this.logger.info(`注册沙箱类型: ${type}`);
        this.sandboxClasses.set(type, SandboxClass);
    }

    /**
     * 注销沙箱类型
     * @param type 沙箱类型
     */
    static unregisterSandboxType(type: SandboxType): void {
        this.logger.info(`注销沙箱类型: ${type}`);
        this.sandboxClasses.delete(type);
    }

    /**
     * 获取支持的沙箱类型
     * @returns 支持的沙箱类型列表
     */
    static getSupportedTypes(): SandboxType[] {
        return Array.from(this.sandboxClasses.keys());
    }

    /**
     * 检查沙箱类型是否支持
     * @param type 沙箱类型
     * @returns 是否支持
     */
    static isSandboxTypeSupported(type: SandboxType): boolean {
        return this.sandboxClasses.has(type);
    }

    /**
     * 获取推荐的沙箱类型
     * @returns 推荐的沙箱类型
     */
    static getRecommendedType(): SandboxType {
        // 根据浏览器能力推荐沙箱类型
        if (typeof Proxy !== 'undefined') {
            return SANDBOX_TYPES.PROXY;
        }

        if (typeof customElements !== 'undefined' && typeof ShadowRoot !== 'undefined') {
            return SANDBOX_TYPES.WEB_COMPONENT;
        }

        return SANDBOX_TYPES.IFRAME;
    }

    /**
     * 验证沙箱选项
     * @param options 沙箱选项
     */
    private static validateSandboxOptions(options: SandboxOptions): void {
        if (!options.name || typeof options.name !== 'string') {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                '沙箱名称必须是非空字符串',
                undefined,
                { options }
            );
        }

        if (options.name.length > 100) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                '沙箱名称长度不能超过100个字符',
                undefined,
                { sandboxName: options.name }
            );
        }

        // 验证名称格式
        if (!/^[a-zA-Z0-9_-]+$/.test(options.name)) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                '沙箱名称只能包含字母、数字、下划线和连字符',
                undefined,
                { sandboxName: options.name }
            );
        }

        // 验证全局变量白名单
        if (options.globalWhitelist && !Array.isArray(options.globalWhitelist)) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                '全局变量白名单必须是数组',
                undefined,
                { sandboxName: options.name }
            );
        }

        // 验证全局变量黑名单
        if (options.globalBlacklist && !Array.isArray(options.globalBlacklist)) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                '全局变量黑名单必须是数组',
                undefined,
                { sandboxName: options.name }
            );
        }
    }

    /**
     * 创建默认沙箱选项
     * @param name 沙箱名称
     * @param type 沙箱类型
     * @returns 默认沙箱选项
     */
    static createDefaultOptions(name: string, type?: SandboxType): SandboxOptions {
        return {
            name,
            type: type || this.getRecommendedType(),
            strict: false,
            globalWhitelist: [],
            globalBlacklist: [
                'top',
                'parent',
                'window',
                'self',
                'globalThis',
                'document',
                'location',
                'history'
            ]
        };
    }

    /**
     * 批量创建沙箱
     * @param configs 沙箱配置数组
     * @returns 沙箱实例数组
     */
    static createSandboxes(configs: SandboxOptions[]): SandboxInstance[] {
        this.logger.info(`批量创建沙箱: ${configs.length}个`);

        const sandboxes: SandboxInstance[] = [];
        const errors: Error[] = [];

        for (const config of configs) {
            try {
                const sandbox = this.createSandbox(config);
                sandboxes.push(sandbox);
            } catch (error) {
                errors.push(error as Error);
                this.logger.error(`沙箱创建失败: ${config.name}`, error);
            }
        }

        if (errors.length > 0) {
            this.logger.warn(`批量创建沙箱完成，成功: ${sandboxes.length}个，失败: ${errors.length}个`);
        } else {
            this.logger.info(`批量创建沙箱成功: ${sandboxes.length}个`);
        }

        return sandboxes;
    }

    /**
     * 检查浏览器兼容性
     * @param type 沙箱类型
     * @returns 兼容性检查结果
     */
    static checkCompatibility(type: SandboxType): {
        supported: boolean;
        reason?: string;
        alternatives?: SandboxType[];
    } {
        switch (type) {
            case SANDBOX_TYPES.PROXY:
                if (typeof Proxy === 'undefined') {
                    return {
                        supported: false,
                        reason: '浏览器不支持Proxy',
                        alternatives: [SANDBOX_TYPES.IFRAME, SANDBOX_TYPES.WEB_COMPONENT]
                    };
                }
                break;

            case SANDBOX_TYPES.WEB_COMPONENT:
                if (typeof customElements === 'undefined' || typeof ShadowRoot === 'undefined') {
                    return {
                        supported: false,
                        reason: '浏览器不支持Web Components',
                        alternatives: [SANDBOX_TYPES.PROXY, SANDBOX_TYPES.IFRAME]
                    };
                }
                break;

            case SANDBOX_TYPES.IFRAME:
                // iframe基本上所有浏览器都支持
                break;

            default:
                return {
                    supported: false,
                    reason: `未知的沙箱类型: ${type}`,
                    alternatives: this.getSupportedTypes()
                };
        }

        return { supported: true };
    }
}

export default SandboxFactory;