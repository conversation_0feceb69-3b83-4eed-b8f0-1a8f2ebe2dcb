/**
 * @fileoverview 基础沙箱实现
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { AppInstance, SandboxContext, SandboxInstance, SandboxOptions } from '@micro-core/shared/types';

/**
 * 基础沙箱抽象类
 * 定义沙箱的基本接口和通用功能
 */
export abstract class BaseSandbox implements SandboxInstance {
    protected logger = createLogger('BaseSandbox');
    protected options: SandboxOptions;
    protected context: SandboxContext | undefined;
    protected isActivated = false;

    /** 沙箱名称 */
    public readonly name: string;
    /** 沙箱类型 */
    public readonly type: string;

    constructor(options: SandboxOptions) {
        this.options = {
            strict: false,
            globalWhitelist: [],
            globalBlacklist: [],
            ...options
        };
        this.name = options.name;
        this.type = 'base';
    }

    /**
     * 是否激活
     */
    get active(): boolean {
        return this.isActivated;
    }

    /**
     * 激活沙箱
     * @param app 微应用实例
     * @returns Promise<void>
     */
    async activate(app: AppInstance): Promise<void> {
        if (this.isActivated) {
            this.logger.warn(`沙箱 "${this.name}" 已经激活`);
            return;
        }

        try {
            this.logger.info(`激活沙箱: ${this.name}`);

            // 创建沙箱上下文
            this.context = await this.createSandboxContext(app);
            this.isActivated = true;

            // 执行激活后的钩子
            await this.onActivated(app);

            this.logger.info(`沙箱激活成功: ${this.name}`);
        } catch (error) {
            this.logger.error(`沙箱激活失败: ${this.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_ACTIVATE_FAILED,
                `沙箱激活失败: ${(error as Error).message}`,
                app.name,
                { sandboxName: this.name },
                error as Error
            );
        }
    }

    /**
     * 停用沙箱
     * @returns Promise<void>
     */
    async deactivate(): Promise<void> {
        if (!this.isActivated) {
            this.logger.warn(`沙箱 "${this.name}" 未激活`);
            return;
        }

        try {
            this.logger.info(`停用沙箱: ${this.name}`);

            // 执行停用前的钩子
            await this.onDeactivating();

            // 清理沙箱上下文
            await this.destroySandboxContext();

            this.context = undefined;
            this.isActivated = false;

            this.logger.info(`沙箱停用成功: ${this.name}`);
        } catch (error) {
            this.logger.error(`沙箱停用失败: ${this.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_DEACTIVATE_FAILED,
                `沙箱停用失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name },
                error as Error
            );
        }
    }

    /**
     * 销毁沙箱
     * @returns Promise<void>
     */
    async destroy(): Promise<void> {
        if (this.isActivated) {
            await this.deactivate();
        }

        try {
            this.logger.info(`销毁沙箱: ${this.name}`);
            await this.onDestroying();
            this.logger.info(`沙箱销毁成功: ${this.name}`);
        } catch (error) {
            this.logger.error(`沙箱销毁失败: ${this.name}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_DESTROY_FAILED,
                `沙箱销毁失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name },
                error as Error
            );
        }
    }

    /**
     * 执行代码
     * @param code 要执行的代码
     * @param filename 文件名（可选）
     * @returns 执行结果
     */
    async execScript(code: string, filename?: string): Promise<any> {
        if (!this.isActivated || !this.context) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `沙箱 "${this.name}" 未激活，无法执行代码`,
                undefined,
                { sandboxName: this.name }
            );
        }

        try {
            this.logger.debug(`执行代码: ${filename || 'inline'}`);
            return await this.doExecScript(code, filename);
        } catch (error) {
            this.logger.error(`代码执行失败: ${filename || 'inline'}`, error);
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `沙箱代码执行失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name, filename },
                error as Error
            );
        }
    }

    /**
     * 获取沙箱上下文
     * @returns SandboxContext | undefined
     */
    getContext(): SandboxContext | undefined {
        return this.context;
    }

    /**
     * 获取沙箱选项
     * @returns SandboxOptions
     */
    getOptions(): SandboxOptions {
        return { ...this.options };
    }

    /**
     * 设置全局变量
     * @param key 变量名
     * @param value 变量值
     */
    setGlobal(key: string, value: any): void {
        if (!this.context) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `沙箱 "${this.name}" 未激活，无法设置全局变量`,
                undefined,
                { sandboxName: this.name, key }
            );
        }

        (this.context.window as any)[key] = value;
        this.logger.debug(`设置全局变量: ${key}`);
    }

    /**
     * 获取全局变量
     * @param key 变量名
     * @returns 变量值
     */
    getGlobal(key: string): any {
        if (!this.context) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `沙箱 "${this.name}" 未激活，无法获取全局变量`,
                undefined,
                { sandboxName: this.name, key }
            );
        }

        return (this.context.window as any)[key];
    }

    /**
     * 创建沙箱上下文（抽象方法）
     * @param app 微应用实例
     * @returns Promise<SandboxContext>
     */
    protected abstract createSandboxContext(app: AppInstance): Promise<SandboxContext>;

    /**
     * 销毁沙箱上下文（抽象方法）
     * @returns Promise<void>
     */
    protected abstract destroySandboxContext(): Promise<void>;

    /**
     * 执行代码的具体实现（抽象方法）
     * @param code 要执行的代码
     * @param filename 文件名
     * @returns Promise<any>
     */
    protected abstract doExecScript(code: string, filename?: string): Promise<any>;

    /**
     * 沙箱激活后的钩子
     * @param app 微应用实例
     * @returns Promise<void>
     */
    protected async onActivated(app: AppInstance): Promise<void> {
        // 子类可以重写此方法
    }

    /**
     * 沙箱停用前的钩子
     * @returns Promise<void>
     */
    protected async onDeactivating(): Promise<void> {
        // 子类可以重写此方法
    }

    /**
     * 沙箱销毁前的钩子
     * @returns Promise<void>
     */
    protected async onDestroying(): Promise<void> {
        // 子类可以重写此方法
    }
}

export default BaseSandbox;