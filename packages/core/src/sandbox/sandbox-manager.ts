/**
 * @fileoverview 沙箱管理器
 * @description 负责管理沙箱的生命周期和状态
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { AppInstance, SandboxInstance, SandboxOptions } from '@micro-core/shared/types';
import { SandboxFactory } from './sandbox-factory';

/**
 * 沙箱管理器类
 * @description 管理沙箱的创建、激活、停用和销毁
 */
export class SandboxManager {
    private logger = createLogger('SandboxManager');
    private sandboxes = new Map<string, SandboxInstance>();
    private appSandboxMap = new Map<string, string>(); // 应用名 -> 沙箱名
    private sandboxStats = new Map<string, SandboxStats>();

    /**
     * 创建沙箱
     * @param options 沙箱配置选项
     * @returns 沙箱实例
     */
    async createSandbox(options: SandboxOptions): Promise<SandboxInstance> {
        const { name } = options;

        // 检查沙箱是否已存在
        if (this.sandboxes.has(name)) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                `沙箱 "${name}" 已存在`,
                undefined,
                { sandboxName: name }
            );
        }

        this.logger.info(`创建沙箱: ${name}`);

        try {
            // 使用工厂创建沙箱
            const sandbox = SandboxFactory.createSandbox(options);

            // 注册沙箱
            this.sandboxes.set(name, sandbox);

            // 初始化统计信息
            this.sandboxStats.set(name, {
                name,
                type: sandbox.type,
                createdAt: Date.now(),
                activationCount: 0,
                lastActivatedAt: 0,
                totalActiveTime: 0,
                errorCount: 0
            });

            this.logger.info(`沙箱创建成功: ${name}`);
            return sandbox;
        } catch (error) {
            this.logger.error(`沙箱创建失败: ${name}`, error);
            throw error;
        }
    }

    /**
     * 获取沙箱
     * @param name 沙箱名称
     * @returns 沙箱实例
     */
    getSandbox(name: string): SandboxInstance | undefined {
        return this.sandboxes.get(name);
    }

    /**
     * 获取应用的沙箱
     * @param appName 应用名称
     * @returns 沙箱实例
     */
    getAppSandbox(appName: string): SandboxInstance | undefined {
        const sandboxName = this.appSandboxMap.get(appName);
        return sandboxName ? this.sandboxes.get(sandboxName) : undefined;
    }

    /**
     * 激活沙箱
     * @param name 沙箱名称
     * @param app 应用实例
     * @returns Promise<void>
     */
    async activateSandbox(name: string, app: AppInstance): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_ACTIVATE_FAILED,
                `沙箱 "${name}" 不存在`,
                app.name,
                { sandboxName: name }
            );
        }

        this.logger.info(`激活沙箱: ${name} for app: ${app.name}`);

        try {
            const startTime = Date.now();

            // 激活沙箱
            await sandbox.activate(app);

            // 建立应用与沙箱的映射
            this.appSandboxMap.set(app.name, name);

            // 更新统计信息
            const stats = this.sandboxStats.get(name);
            if (stats) {
                stats.activationCount++;
                stats.lastActivatedAt = startTime;
            }

            this.logger.info(`沙箱激活成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱激活失败: ${name}`, error);

            // 更新错误统计
            const stats = this.sandboxStats.get(name);
            if (stats) {
                stats.errorCount++;
            }

            throw error;
        }
    }

    /**
     * 停用沙箱
     * @param name 沙箱名称
     * @returns Promise<void>
     */
    async deactivateSandbox(name: string): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            this.logger.warn(`沙箱 "${name}" 不存在，无法停用`);
            return;
        }

        this.logger.info(`停用沙箱: ${name}`);

        try {
            const startTime = Date.now();

            // 停用沙箱
            await sandbox.deactivate();

            // 移除应用与沙箱的映射
            for (const [appName, sandboxName] of this.appSandboxMap.entries()) {
                if (sandboxName === name) {
                    this.appSandboxMap.delete(appName);
                    break;
                }
            }

            // 更新统计信息
            const stats = this.sandboxStats.get(name);
            if (stats && stats.lastActivatedAt > 0) {
                stats.totalActiveTime += startTime - stats.lastActivatedAt;
            }

            this.logger.info(`沙箱停用成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱停用失败: ${name}`, error);

            // 更新错误统计
            const stats = this.sandboxStats.get(name);
            if (stats) {
                stats.errorCount++;
            }

            throw error;
        }
    }

    /**
     * 停用应用的沙箱
     * @param appName 应用名称
     * @returns Promise<void>
     */
    async deactivateAppSandbox(appName: string): Promise<void> {
        const sandboxName = this.appSandboxMap.get(appName);
        if (sandboxName) {
            await this.deactivateSandbox(sandboxName);
        }
    }

    /**
     * 销毁沙箱
     * @param name 沙箱名称
     * @returns Promise<void>
     */
    async destroySandbox(name: string): Promise<void> {
        const sandbox = this.sandboxes.get(name);
        if (!sandbox) {
            this.logger.warn(`沙箱 "${name}" 不存在，无法销毁`);
            return;
        }

        this.logger.info(`销毁沙箱: ${name}`);

        try {
            // 先停用沙箱
            if (sandbox.active) {
                await this.deactivateSandbox(name);
            }

            // 销毁沙箱
            await sandbox.destroy();

            // 移除沙箱
            this.sandboxes.delete(name);
            this.sandboxStats.delete(name);

            // 清理应用映射
            for (const [appName, sandboxName] of this.appSandboxMap.entries()) {
                if (sandboxName === name) {
                    this.appSandboxMap.delete(appName);
                }
            }

            this.logger.info(`沙箱销毁成功: ${name}`);
        } catch (error) {
            this.logger.error(`沙箱销毁失败: ${name}`, error);
            throw error;
        }
    }

    /**
     * 销毁所有沙箱
     * @returns Promise<void>
     */
    async destroyAllSandboxes(): Promise<void> {
        this.logger.info('销毁所有沙箱');

        const sandboxNames = Array.from(this.sandboxes.keys());
        const errors: Error[] = [];

        for (const name of sandboxNames) {
            try {
                await this.destroySandbox(name);
            } catch (error) {
                errors.push(error as Error);
            }
        }

        if (errors.length > 0) {
            this.logger.warn(`销毁沙箱完成，失败: ${errors.length}个`);
        } else {
            this.logger.info('所有沙箱销毁成功');
        }
    }

    /**
     * 获取所有沙箱
     * @returns 沙箱实例数组
     */
    getAllSandboxes(): SandboxInstance[] {
        return Array.from(this.sandboxes.values());
    }

    /**
     * 获取沙箱名称列表
     * @returns 沙箱名称数组
     */
    getSandboxNames(): string[] {
        return Array.from(this.sandboxes.keys());
    }

    /**
     * 获取激活的沙箱
     * @returns 激活的沙箱实例数组
     */
    getActiveSandboxes(): SandboxInstance[] {
        return Array.from(this.sandboxes.values()).filter(sandbox => sandbox.active);
    }

    /**
     * 检查沙箱是否存在
     * @param name 沙箱名称
     * @returns 是否存在
     */
    hasSandbox(name: string): boolean {
        return this.sandboxes.has(name);
    }

    /**
     * 获取沙箱统计信息
     * @param name 沙箱名称
     * @returns 统计信息
     */
    getSandboxStats(name: string): SandboxStats | undefined {
        return this.sandboxStats.get(name);
    }

    /**
     * 获取所有沙箱统计信息
     * @returns 统计信息数组
     */
    getAllSandboxStats(): SandboxStats[] {
        return Array.from(this.sandboxStats.values());
    }

    /**
     * 重置沙箱统计信息
     * @param name 沙箱名称
     */
    resetSandboxStats(name: string): void {
        const stats = this.sandboxStats.get(name);
        if (stats) {
            stats.activationCount = 0;
            stats.lastActivatedAt = 0;
            stats.totalActiveTime = 0;
            stats.errorCount = 0;
        }
    }

    /**
     * 获取管理器状态
     * @returns 管理器状态
     */
    getManagerStatus(): {
        totalSandboxes: number;
        activeSandboxes: number;
        sandboxTypes: Record<string, number>;
        totalActivations: number;
        totalErrors: number;
    } {
        const sandboxes = Array.from(this.sandboxes.values());
        const stats = Array.from(this.sandboxStats.values());

        const sandboxTypes: Record<string, number> = {};
        sandboxes.forEach(sandbox => {
            sandboxTypes[sandbox.type] = (sandboxTypes[sandbox.type] || 0) + 1;
        });

        return {
            totalSandboxes: sandboxes.length,
            activeSandboxes: sandboxes.filter(s => s.active).length,
            sandboxTypes,
            totalActivations: stats.reduce((sum, s) => sum + s.activationCount, 0),
            totalErrors: stats.reduce((sum, s) => sum + s.errorCount, 0)
        };
    }

    /**
     * 清理无效的沙箱
     * @returns Promise<number> 清理的沙箱数量
     */
    async cleanupInvalidSandboxes(): Promise<number> {
        this.logger.info('清理无效的沙箱');

        const invalidSandboxes: string[] = [];

        for (const [name, sandbox] of this.sandboxes.entries()) {
            try {
                // 检查沙箱是否有效
                if (!sandbox || typeof sandbox.activate !== 'function') {
                    invalidSandboxes.push(name);
                }
            } catch (error) {
                this.logger.warn(`检查沙箱 ${name} 时出错:`, error);
                invalidSandboxes.push(name);
            }
        }

        // 清理无效沙箱
        for (const name of invalidSandboxes) {
            try {
                await this.destroySandbox(name);
            } catch (error) {
                this.logger.error(`清理无效沙箱 ${name} 失败:`, error);
            }
        }

        this.logger.info(`清理完成，清理了 ${invalidSandboxes.length} 个无效沙箱`);
        return invalidSandboxes.length;
    }
}

/**
 * 沙箱统计信息接口
 */
export interface SandboxStats {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: string;
    /** 创建时间 */
    createdAt: number;
    /** 激活次数 */
    activationCount: number;
    /** 最后激活时间 */
    lastActivatedAt: number;
    /** 总激活时间 */
    totalActiveTime: number;
    /** 错误次数 */
    errorCount: number;
}

export default SandboxManager;