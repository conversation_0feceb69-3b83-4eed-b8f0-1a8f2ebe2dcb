/**
 * @fileoverview WebComponent沙箱实现
 * @description 基于Web Components的沙箱隔离，利用Shadow DOM实现样式和DOM隔离
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { AppInstance, SandboxContext } from '@micro-core/shared/types';
import { BaseSandbox } from './base-sandbox';

/**
 * WebComponent沙箱实现
 * @description 使用Web Components和Shadow DOM创建隔离的执行环境
 */
export class WebComponentSandbox extends BaseSandbox {
    private customElement: HTMLElement | null = null;
    private shadowRoot: ShadowRoot | null = null;
    private sandboxWindow: Record<string, any> = Object.create(null);
    private elementName: string;

    constructor(options: any) {
        super(options);
        this.type = 'web-component';
        this.logger = createLogger(`WebComponentSandbox:${this.name}`);
        this.elementName = `micro-core-sandbox-${this.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
    }

    /**
     * 创建沙箱上下文
     * @param app 微应用实例
     * @returns Promise<SandboxContext>
     */
    protected async createSandboxContext(app: AppInstance): Promise<SandboxContext> {
        // 检查浏览器支持
        if (!this.checkWebComponentSupport()) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_TYPE_UNSUPPORTED,
                '当前浏览器不支持Web Components',
                app.name,
                { sandboxName: this.name }
            );
        }

        // 定义自定义元素
        await this.defineCustomElement();

        // 创建自定义元素实例
        this.customElement = document.createElement(this.elementName);
        this.shadowRoot = this.customElement.shadowRoot;

        if (!this.shadowRoot) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                'Shadow DOM创建失败',
                app.name,
                { sandboxName: this.name }
            );
        }

        // 设置沙箱环境
        await this.setupSandboxEnvironment();

        // 创建沙箱window代理
        const sandboxWindow = this.createSandboxWindow();

        return {
            name: this.name,
            active: true,
            window: sandboxWindow,
            document: this.shadowRoot as any,
            createdAt: Date.now()
        };
    }

    /**
     * 销毁沙箱上下文
     * @returns Promise<void>
     */
    protected async destroySandboxContext(): Promise<void> {
        // 移除自定义元素
        if (this.customElement && this.customElement.parentNode) {
            this.customElement.parentNode.removeChild(this.customElement);
        }

        // 清理引用
        this.customElement = null;
        this.shadowRoot = null;
        this.sandboxWindow = Object.create(null);

        // 注销自定义元素（如果可能）
        try {
            if (customElements.get(this.elementName)) {
                // 注意：customElements.define是不可逆的，这里只是清理引用
                this.logger.warn(`自定义元素 ${this.elementName} 无法注销，这是Web Components的限制`);
            }
        } catch (error) {
            this.logger.warn('清理自定义元素时出错:', error);
        }
    }

    /**
     * 执行代码的具体实现
     * @param code 要执行的代码
     * @param filename 文件名
     * @returns Promise<any>
     */
    protected async doExecScript(code: string, filename?: string): Promise<any> {
        if (!this.shadowRoot || !this.sandboxWindow) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                'WebComponent沙箱未初始化',
                undefined,
                { sandboxName: this.name }
            );
        }

        try {
            // 在沙箱环境中执行代码
            const execScript = new Function(
                'window',
                'document',
                'self',
                'globalThis',
                'shadowRoot',
                code
            );

            return execScript(
                this.sandboxWindow,
                this.shadowRoot,
                this.sandboxWindow,
                this.sandboxWindow,
                this.shadowRoot
            );
        } catch (error) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `WebComponent代码执行失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name, filename },
                error as Error
            );
        }
    }

    /**
     * 检查Web Components支持
     * @returns 是否支持
     */
    private checkWebComponentSupport(): boolean {
        return !!(
            window.customElements &&
            window.customElements.define &&
            window.ShadowRoot &&
            HTMLElement.prototype.attachShadow
        );
    }

    /**
     * 定义自定义元素
     * @returns Promise<void>
     */
    private async defineCustomElement(): Promise<void> {
        // 检查元素是否已定义
        if (customElements.get(this.elementName)) {
            this.logger.warn(`自定义元素 ${this.elementName} 已存在`);
            return;
        }

        const sandboxName = this.name;
        const logger = this.logger;

        class MicroCoreSandboxElement extends HTMLElement {
            private _shadowRoot: ShadowRoot;

            constructor() {
                super();
                this._shadowRoot = this.attachShadow({ mode: 'closed' });
                this.setupShadowDOM();
            }

            private setupShadowDOM(): void {
                // 创建基础HTML结构
                this._shadowRoot.innerHTML = `
                    <style>
                        :host {
                            display: block;
                            position: relative;
                            isolation: isolate;
                        }
                        
                        .sandbox-container {
                            width: 100%;
                            height: 100%;
                            overflow: hidden;
                        }
                        
                        .sandbox-content {
                            width: 100%;
                            height: 100%;
                        }
                    </style>
                    <div class="sandbox-container">
                        <div class="sandbox-content" id="sandbox-content"></div>
                    </div>
                `;

                logger.debug(`自定义元素 ${sandboxName} Shadow DOM 已设置`);
            }

            connectedCallback(): void {
                logger.debug(`自定义元素 ${sandboxName} 已连接到DOM`);
            }

            disconnectedCallback(): void {
                logger.debug(`自定义元素 ${sandboxName} 已从DOM断开`);
            }

            get shadowRoot(): ShadowRoot {
                return this._shadowRoot;
            }
        }

        // 定义自定义元素
        customElements.define(this.elementName, MicroCoreSandboxElement);

        // 等待元素定义完成
        await customElements.whenDefined(this.elementName);
    }

    /**
     * 设置沙箱环境
     * @returns Promise<void>
     */
    private async setupSandboxEnvironment(): Promise<void> {
        if (!this.shadowRoot) return;

        // 注入沙箱样式
        this.injectSandboxStyles();

        // 设置事件隔离
        this.setupEventIsolation();
    }

    /**
     * 注入沙箱样式
     */
    private injectSandboxStyles(): void {
        if (!this.shadowRoot) return;

        const style = document.createElement('style');
        style.textContent = `
            /* 沙箱样式重置 */
            * {
                box-sizing: border-box;
            }
            
            /* 防止样式泄露 */
            :host {
                all: initial;
                display: block;
            }
            
            /* 自定义沙箱样式 */
            .micro-core-sandbox {
                position: relative;
                width: 100%;
                height: 100%;
                overflow: auto;
            }
        `;

        this.shadowRoot.appendChild(style);
    }

    /**
     * 设置事件隔离
     */
    private setupEventIsolation(): void {
        if (!this.shadowRoot) return;

        // 阻止事件冒泡到外部
        const stopPropagation = (event: Event) => {
            if (this.options.strict) {
                event.stopPropagation();
            }
        };

        // 监听常见事件
        const events = ['click', 'mousedown', 'mouseup', 'keydown', 'keyup', 'focus', 'blur'];
        events.forEach(eventType => {
            this.shadowRoot!.addEventListener(eventType, stopPropagation, true);
        });
    }

    /**
     * 创建沙箱window代理
     * @returns 沙箱window对象
     */
    private createSandboxWindow(): Record<string, any> {
        const originalWindow = window;
        const shadowRoot = this.shadowRoot;
        const sandboxWindow = this.sandboxWindow;

        // 基础属性
        sandboxWindow.window = sandboxWindow;
        sandboxWindow.self = sandboxWindow;
        sandboxWindow.globalThis = sandboxWindow;
        sandboxWindow.document = shadowRoot;

        // 复制安全的window属性
        const safeProperties = [
            'console',
            'setTimeout',
            'clearTimeout',
            'setInterval',
            'clearInterval',
            'requestAnimationFrame',
            'cancelAnimationFrame',
            'Date',
            'Math',
            'JSON',
            'Object',
            'Array',
            'String',
            'Number',
            'Boolean',
            'RegExp',
            'Error',
            'Promise',
            'Map',
            'Set',
            'WeakMap',
            'WeakSet'
        ];

        safeProperties.forEach(prop => {
            if (prop in originalWindow) {
                sandboxWindow[prop] = (originalWindow as any)[prop];
            }
        });

        // 设置沙箱标识
        sandboxWindow.__MICRO_CORE_SANDBOX__ = {
            name: this.name,
            type: this.type,
            active: true,
            shadowRoot: shadowRoot
        };

        // 代理console（如果需要）
        if (this.options.strict) {
            sandboxWindow.console = this.createConsoleProxy();
        }

        // 设置自定义全局变量
        if (this.options.globalWhitelist) {
            this.options.globalWhitelist.forEach(key => {
                if (key in originalWindow) {
                    sandboxWindow[key] = (originalWindow as any)[key];
                }
            });
        }

        return sandboxWindow;
    }

    /**
     * 创建console代理
     * @returns 代理的console对象
     */
    private createConsoleProxy(): Console {
        const originalConsole = window.console;
        const sandboxName = this.name;

        return new Proxy(originalConsole, {
            get(target, prop) {
                const value = target[prop as keyof Console];
                if (typeof value === 'function') {
                    return function (...args: any[]) {
                        const prefixedArgs = [`[${sandboxName}]`, ...args];
                        return (value as Function).apply(target, prefixedArgs);
                    };
                }
                return value;
            }
        });
    }

    /**
     * 获取自定义元素
     * @returns 自定义元素
     */
    getCustomElement(): HTMLElement | null {
        return this.customElement;
    }

    /**
     * 获取Shadow Root
     * @returns Shadow Root
     */
    getShadowRoot(): ShadowRoot | null {
        return this.shadowRoot;
    }

    /**
     * 获取元素名称
     * @returns 元素名称
     */
    getElementName(): string {
        return this.elementName;
    }

    /**
     * 向Shadow DOM添加内容
     * @param content HTML内容
     */
    appendContent(content: string): void {
        if (!this.shadowRoot) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                'Shadow DOM未初始化',
                undefined,
                { sandboxName: this.name }
            );
        }

        const container = this.shadowRoot.querySelector('#sandbox-content');
        if (container) {
            container.innerHTML = content;
        }
    }

    /**
     * 清空Shadow DOM内容
     */
    clearContent(): void {
        if (!this.shadowRoot) return;

        const container = this.shadowRoot.querySelector('#sandbox-content');
        if (container) {
            container.innerHTML = '';
        }
    }
}

export default WebComponentSandbox;