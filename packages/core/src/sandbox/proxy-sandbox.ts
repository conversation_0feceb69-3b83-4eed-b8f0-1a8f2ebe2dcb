/**
 * @fileoverview Proxy沙箱实现
 * @description 基于Proxy的沙箱隔离，提供最佳的兼容性和性能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { AppInstance, SandboxContext } from '@micro-core/shared/types';
import { BaseSandbox } from './base-sandbox';

/**
 * Proxy沙箱实现
 * @description 使用Proxy代理window对象，实现全局变量隔离
 */
export class ProxySandbox extends BaseSandbox {
    private fakeWindow: Record<string, any> = Object.create(null);
    private proxyWindow: Window | null = null;
    private addedPropsMapInSandbox = new Map<string, any>();
    private modifiedPropsOriginalValueMapInSandbox = new Map<string, any>();
    private currentUpdatedPropsValueMap = new Map<string, any>();

    constructor(options: any) {
        super(options);
        this.type = 'proxy';
        this.logger = createLogger(`ProxySandbox:${this.name}`);
    }

    /**
     * 创建沙箱上下文
     * @param app 微应用实例
     * @returns Promise<SandboxContext>
     */
    protected async createSandboxContext(app: AppInstance): Promise<SandboxContext> {
        const rawWindow = window;
        const fakeWindow = this.fakeWindow;

        // 创建代理对象
        const proxy = new Proxy(rawWindow, {
            get: (target: Window, prop: string | symbol): any => {
                // 优先从沙箱环境获取
                if (prop in fakeWindow) {
                    return fakeWindow[prop as string];
                }

                // 从原始window获取
                const value = target[prop as keyof Window];

                // 如果是函数，需要绑定正确的this
                if (typeof value === 'function') {
                    return value.bind(target);
                }

                return value;
            },

            set: (target: Window, prop: string | symbol, value: any): boolean => {
                const propStr = prop as string;

                // 检查是否在黑名单中
                if (this.isPropertyBlacklisted(propStr)) {
                    this.logger.warn(`尝试设置黑名单属性: ${propStr}`);
                    return false;
                }

                // 记录属性变更
                if (!target.hasOwnProperty(propStr)) {
                    // 新增属性
                    this.addedPropsMapInSandbox.set(propStr, value);
                } else if (!this.modifiedPropsOriginalValueMapInSandbox.has(propStr)) {
                    // 修改已有属性，记录原始值
                    this.modifiedPropsOriginalValueMapInSandbox.set(propStr, target[propStr as keyof Window]);
                }

                // 记录当前值
                this.currentUpdatedPropsValueMap.set(propStr, value);

                // 设置到沙箱环境
                fakeWindow[propStr] = value;

                return true;
            },

            has: (target: Window, prop: string | symbol): boolean => {
                const propStr = prop as string;
                return propStr in fakeWindow || propStr in target;
            },

            deleteProperty: (target: Window, prop: string | symbol): boolean => {
                const propStr = prop as string;

                if (propStr in fakeWindow) {
                    delete fakeWindow[propStr];
                    this.currentUpdatedPropsValueMap.delete(propStr);
                    return true;
                }

                return true;
            }
        });

        this.proxyWindow = proxy as Window;

        // 设置沙箱全局变量
        this.setupSandboxGlobals();

        return {
            name: this.name,
            active: true,
            window: this.proxyWindow,
            document: document,
            createdAt: Date.now()
        };
    }

    /**
     * 销毁沙箱上下文
     * @returns Promise<void>
     */
    protected async destroySandboxContext(): Promise<void> {
        // 恢复原始属性值
        this.modifiedPropsOriginalValueMapInSandbox.forEach((value, prop) => {
            (window as any)[prop] = value;
        });

        // 删除新增属性
        this.addedPropsMapInSandbox.forEach((_, prop) => {
            delete (window as any)[prop];
        });

        // 清理记录
        this.addedPropsMapInSandbox.clear();
        this.modifiedPropsOriginalValueMapInSandbox.clear();
        this.currentUpdatedPropsValueMap.clear();

        // 清理沙箱环境
        Object.keys(this.fakeWindow).forEach(key => {
            delete this.fakeWindow[key];
        });

        this.proxyWindow = null;
    }

    /**
     * 执行代码的具体实现
     * @param code 要执行的代码
     * @param filename 文件名
     * @returns Promise<any>
     */
    protected async doExecScript(code: string, filename?: string): Promise<any> {
        if (!this.proxyWindow) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                '沙箱未初始化',
                undefined,
                { sandboxName: this.name }
            );
        }

        try {
            // 创建执行函数
            const execScript = new Function('window', 'document', 'self', 'globalThis', code);

            // 在沙箱环境中执行
            return execScript(
                this.proxyWindow,
                document,
                this.proxyWindow,
                this.proxyWindow
            );
        } catch (error) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `代码执行失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name, filename },
                error as Error
            );
        }
    }

    /**
     * 设置沙箱全局变量
     */
    private setupSandboxGlobals(): void {
        if (!this.proxyWindow) return;

        // 设置自定义全局变量
        if (this.options.globalWhitelist) {
            this.options.globalWhitelist.forEach(key => {
                if (key in window) {
                    this.fakeWindow[key] = (window as any)[key];
                }
            });
        }

        // 设置沙箱标识
        this.fakeWindow.__MICRO_CORE_SANDBOX__ = {
            name: this.name,
            type: this.type,
            active: true
        };

        // 设置console代理（可选）
        if (this.options.strict) {
            this.fakeWindow.console = this.createConsoleProxy();
        }
    }

    /**
     * 创建console代理
     * @returns 代理的console对象
     */
    private createConsoleProxy(): Console {
        const originalConsole = window.console;
        const sandboxName = this.name;

        return new Proxy(originalConsole, {
            get(target, prop) {
                const value = target[prop as keyof Console];
                if (typeof value === 'function') {
                    return function (...args: any[]) {
                        const prefixedArgs = [`[${sandboxName}]`, ...args];
                        return (value as Function).apply(target, prefixedArgs);
                    };
                }
                return value;
            }
        });
    }

    /**
     * 检查属性是否在黑名单中
     * @param prop 属性名
     * @returns 是否在黑名单中
     */
    private isPropertyBlacklisted(prop: string): boolean {
        // 默认黑名单
        const defaultBlacklist = [
            'top',
            'parent',
            'window',
            'self',
            'globalThis',
            'document',
            'location',
            'history'
        ];

        const blacklist = [
            ...defaultBlacklist,
            ...(this.options.globalBlacklist || [])
        ];

        return blacklist.includes(prop);
    }

    /**
     * 获取沙箱快照
     * @returns 沙箱状态快照
     */
    getSnapshot(): Record<string, any> {
        return {
            addedProps: Array.from(this.addedPropsMapInSandbox.entries()),
            modifiedProps: Array.from(this.modifiedPropsOriginalValueMapInSandbox.entries()),
            currentProps: Array.from(this.currentUpdatedPropsValueMap.entries()),
            fakeWindow: { ...this.fakeWindow }
        };
    }

    /**
     * 恢复沙箱快照
     * @param snapshot 沙箱状态快照
     */
    restoreSnapshot(snapshot: Record<string, any>): void {
        // 清理当前状态
        this.addedPropsMapInSandbox.clear();
        this.modifiedPropsOriginalValueMapInSandbox.clear();
        this.currentUpdatedPropsValueMap.clear();
        Object.keys(this.fakeWindow).forEach(key => {
            delete this.fakeWindow[key];
        });

        // 恢复快照状态
        if (snapshot.addedProps) {
            snapshot.addedProps.forEach(([key, value]: [string, any]) => {
                this.addedPropsMapInSandbox.set(key, value);
            });
        }

        if (snapshot.modifiedProps) {
            snapshot.modifiedProps.forEach(([key, value]: [string, any]) => {
                this.modifiedPropsOriginalValueMapInSandbox.set(key, value);
            });
        }

        if (snapshot.currentProps) {
            snapshot.currentProps.forEach(([key, value]: [string, any]) => {
                this.currentUpdatedPropsValueMap.set(key, value);
            });
        }

        if (snapshot.fakeWindow) {
            Object.assign(this.fakeWindow, snapshot.fakeWindow);
        }
    }
}

export default ProxySandbox;