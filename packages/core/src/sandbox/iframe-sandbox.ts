/**
 * @fileoverview iframe沙箱实现
 * @description 基于iframe的沙箱隔离，提供最强的隔离性
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger, ERROR_CODES, MicroCoreError } from '@micro-core/shared';
import type { AppInstance, SandboxContext } from '@micro-core/shared/types';
import { BaseSandbox } from './base-sandbox';

/**
 * iframe沙箱实现
 * @description 使用iframe创建完全隔离的执行环境
 */
export class IframeSandbox extends BaseSandbox {
    private iframe: HTMLIFrameElement | null = null;
    private iframeWindow: Window | null = null;
    private iframeDocument: Document | null = null;
    private messageHandlers = new Map<string, Function>();
    private messageId = 0;

    constructor(options: any) {
        super(options);
        this.type = 'iframe';
        this.logger = createLogger(`IframeSandbox:${this.name}`);
    }

    /**
     * 创建沙箱上下文
     * @param app 微应用实例
     * @returns Promise<SandboxContext>
     */
    protected async createSandboxContext(app: AppInstance): Promise<SandboxContext> {
        // 创建iframe元素
        this.iframe = document.createElement('iframe');
        this.iframe.style.display = 'none';
        this.iframe.src = 'about:blank';
        this.iframe.sandbox = 'allow-scripts allow-same-origin';

        // 设置iframe属性
        this.iframe.setAttribute('name', `micro-core-sandbox-${this.name}`);
        this.iframe.setAttribute('id', `micro-core-sandbox-${this.name}`);

        // 添加到DOM
        document.body.appendChild(this.iframe);

        // 等待iframe加载完成
        await this.waitForIframeLoad();

        // 获取iframe的window和document
        this.iframeWindow = this.iframe.contentWindow;
        this.iframeDocument = this.iframe.contentDocument;

        if (!this.iframeWindow || !this.iframeDocument) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_CREATE_FAILED,
                'iframe沙箱创建失败：无法获取iframe的window或document',
                app.name,
                { sandboxName: this.name }
            );
        }

        // 设置沙箱环境
        await this.setupSandboxEnvironment();

        // 设置消息通信
        this.setupMessageCommunication();

        return {
            name: this.name,
            active: true,
            window: this.iframeWindow as any,
            document: this.iframeDocument,
            createdAt: Date.now()
        };
    }

    /**
     * 销毁沙箱上下文
     * @returns Promise<void>
     */
    protected async destroySandboxContext(): Promise<void> {
        // 清理消息处理器
        this.messageHandlers.clear();

        // 移除iframe
        if (this.iframe && this.iframe.parentNode) {
            this.iframe.parentNode.removeChild(this.iframe);
        }

        // 清理引用
        this.iframe = null;
        this.iframeWindow = null;
        this.iframeDocument = null;
    }

    /**
     * 执行代码的具体实现
     * @param code 要执行的代码
     * @param filename 文件名
     * @returns Promise<any>
     */
    protected async doExecScript(code: string, filename?: string): Promise<any> {
        if (!this.iframeWindow) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                'iframe沙箱未初始化',
                undefined,
                { sandboxName: this.name }
            );
        }

        try {
            // 通过消息传递执行代码
            const messageId = this.generateMessageId();
            const promise = this.createExecutionPromise(messageId);

            // 发送执行消息
            this.iframeWindow.postMessage({
                type: 'EXEC_SCRIPT',
                id: messageId,
                code,
                filename
            }, '*');

            return await promise;
        } catch (error) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                `iframe代码执行失败: ${(error as Error).message}`,
                undefined,
                { sandboxName: this.name, filename },
                error as Error
            );
        }
    }

    /**
     * 等待iframe加载完成
     * @returns Promise<void>
     */
    private waitForIframeLoad(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.iframe) {
                reject(new Error('iframe未创建'));
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('iframe加载超时'));
            }, 10000);

            this.iframe.onload = () => {
                clearTimeout(timeout);
                resolve();
            };

            this.iframe.onerror = () => {
                clearTimeout(timeout);
                reject(new Error('iframe加载失败'));
            };
        });
    }

    /**
     * 设置沙箱环境
     * @returns Promise<void>
     */
    private async setupSandboxEnvironment(): Promise<void> {
        if (!this.iframeWindow || !this.iframeDocument) {
            return;
        }

        // 注入沙箱运行时代码
        const runtimeCode = this.generateSandboxRuntime();

        const script = this.iframeDocument.createElement('script');
        script.textContent = runtimeCode;
        this.iframeDocument.head.appendChild(script);

        // 设置沙箱全局变量
        this.iframeWindow.__MICRO_CORE_SANDBOX__ = {
            name: this.name,
            type: this.type,
            active: true
        };

        // 代理console（如果需要）
        if (this.options.strict) {
            this.setupConsoleProxy();
        }
    }

    /**
     * 生成沙箱运行时代码
     * @returns 运行时代码字符串
     */
    private generateSandboxRuntime(): string {
        return `
            (function() {
                // 沙箱运行时环境
                window.__MICRO_CORE_IFRAME_SANDBOX__ = true;
                
                // 消息处理器
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'EXEC_SCRIPT') {
                        try {
                            // 执行代码
                            const result = (function() {
                                return eval(event.data.code);
                            })();
                            
                            // 发送执行结果
                            parent.postMessage({
                                type: 'EXEC_RESULT',
                                id: event.data.id,
                                success: true,
                                result: result
                            }, '*');
                        } catch (error) {
                            // 发送执行错误
                            parent.postMessage({
                                type: 'EXEC_RESULT',
                                id: event.data.id,
                                success: false,
                                error: {
                                    name: error.name,
                                    message: error.message,
                                    stack: error.stack
                                }
                            }, '*');
                        }
                    }
                });
                
                // 重写一些全局对象以增强安全性
                if (${this.options.strict}) {
                    // 禁用一些危险的API
                    delete window.fetch;
                    delete window.XMLHttpRequest;
                    
                    // 重写location
                    Object.defineProperty(window, 'location', {
                        get: function() {
                            return {
                                href: 'about:blank',
                                origin: 'null',
                                protocol: 'about:',
                                host: '',
                                hostname: '',
                                port: '',
                                pathname: 'blank',
                                search: '',
                                hash: ''
                            };
                        },
                        set: function() {
                            // 禁止修改location
                        }
                    });
                }
            })();
        `;
    }

    /**
     * 设置消息通信
     */
    private setupMessageCommunication(): void {
        window.addEventListener('message', (event) => {
            if (event.source !== this.iframeWindow) {
                return;
            }

            const { type, id, success, result, error } = event.data;

            if (type === 'EXEC_RESULT') {
                const handler = this.messageHandlers.get(id);
                if (handler) {
                    this.messageHandlers.delete(id);
                    if (success) {
                        handler.resolve(result);
                    } else {
                        const err = new Error(error.message);
                        err.name = error.name;
                        err.stack = error.stack;
                        handler.reject(err);
                    }
                }
            }
        });
    }

    /**
     * 设置console代理
     */
    private setupConsoleProxy(): void {
        if (!this.iframeWindow) return;

        const sandboxName = this.name;
        const originalConsole = this.iframeWindow.console;

        this.iframeWindow.console = new Proxy(originalConsole, {
            get(target, prop) {
                const value = target[prop as keyof Console];
                if (typeof value === 'function') {
                    return function (...args: any[]) {
                        const prefixedArgs = [`[${sandboxName}]`, ...args];
                        return (value as Function).apply(target, prefixedArgs);
                    };
                }
                return value;
            }
        });
    }

    /**
     * 生成消息ID
     * @returns 消息ID
     */
    private generateMessageId(): string {
        return `${this.name}-${++this.messageId}-${Date.now()}`;
    }

    /**
     * 创建执行Promise
     * @param messageId 消息ID
     * @returns Promise
     */
    private createExecutionPromise(messageId: string): Promise<any> {
        return new Promise((resolve, reject) => {
            this.messageHandlers.set(messageId, { resolve, reject });

            // 设置超时
            setTimeout(() => {
                if (this.messageHandlers.has(messageId)) {
                    this.messageHandlers.delete(messageId);
                    reject(new Error('代码执行超时'));
                }
            }, 30000);
        });
    }

    /**
     * 向iframe发送消息
     * @param message 消息内容
     * @returns Promise<any>
     */
    async sendMessage(message: any): Promise<any> {
        if (!this.iframeWindow) {
            throw new MicroCoreError(
                ERROR_CODES.SANDBOX_EXEC_FAILED,
                'iframe沙箱未初始化',
                undefined,
                { sandboxName: this.name }
            );
        }

        const messageId = this.generateMessageId();
        const promise = this.createExecutionPromise(messageId);

        this.iframeWindow.postMessage({
            ...message,
            id: messageId
        }, '*');

        return promise;
    }

    /**
     * 获取iframe元素
     * @returns iframe元素
     */
    getIframe(): HTMLIFrameElement | null {
        return this.iframe;
    }

    /**
     * 获取iframe的window对象
     * @returns iframe的window对象
     */
    getIframeWindow(): Window | null {
        return this.iframeWindow;
    }

    /**
     * 获取iframe的document对象
     * @returns iframe的document对象
     */
    getIframeDocument(): Document | null {
        return this.iframeDocument;
    }
}

export default IframeSandbox;