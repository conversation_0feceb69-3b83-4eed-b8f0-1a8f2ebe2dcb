{"name": "@micro-core/core", "version": "0.1.0", "description": "Micro-Core 核心运行时 - 微前端应用的核心引擎，提供应用注册、生命周期管理、沙箱隔离等核心功能", "keywords": ["micro-core", "micro-frontend", "microfrontend", "core", "runtime", "sandbox", "lifecycle"], "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/core"}, "license": "MIT", "author": {"name": "Echo", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "type-check": "tsc --noEmit", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest", "preview": "vite preview"}, "dependencies": {"@micro-core/shared": "workspace:*", "eventemitter3": "^5.0.1"}, "devDependencies": {"@types/node": "^20.10.6", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^8.57.0", "jsdom": "^23.0.1", "typescript": "^5.3.3", "vitest": "^3.2.4", "vite": "^7.0.6", "vite-plugin-dts": "^4.3.0"}, "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}