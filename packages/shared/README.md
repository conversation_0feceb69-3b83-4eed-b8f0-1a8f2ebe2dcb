# @micro-core/shared

Micro-Core 共享工具包，提供核心常量、类型定义、工具函数和错误处理。

## 特性

- 🎯 **统一常量** - 集中管理所有常量定义
- 🔧 **类型安全** - 完整的 TypeScript 类型支持
- 🛠️ **工具函数** - 常用工具函数集合
- ❌ **错误处理** - 统一的错误处理机制

## 安装

```bash
pnpm add @micro-core/shared
```

## 使用

```typescript
import { APP_STATUS, AppConfig, logger, MicroCoreError } from '@micro-core/shared';

// 使用常量
console.log(APP_STATUS.MOUNTED);

// 使用类型
const config: AppConfig = {
    name: 'my-app',
    entry: 'http://localhost:3000',
    container: '#app',
    activeWhen: '/my-app'
};

// 使用工具函数
logger.info('应用启动');

// 使用错误处理
throw new MicroCoreError(2000, '应用加载失败');
```

## API 文档

### 常量

- `APP_STATUS` - 应用状态常量
- `SANDBOX_TYPES` - 沙箱类型常量
- `LIFECYCLE_HOOKS` - 生命周期钩子常量
- `EVENT_TYPES` - 事件类型常量
- `ERROR_CODES` - 错误码常量

### 类型

- `AppConfig` - 应用配置接口
- `AppInstance` - 应用实例接口
- `SandboxInstance` - 沙箱实例接口
- `Plugin` - 插件接口
- `MicroCoreKernel` - 内核接口

### 工具函数

- `isString`, `isFunction`, `isObject` - 类型检查
- `kebabCase`, `camelCase` - 字符串转换
- `deepClone` - 深度克隆
- `sleep`, `timeout` - 异步工具
- `loadScript` - DOM 工具
- `validateAppConfig` - 验证工具
- `logger` - 日志工具

### 错误处理

- `MicroCoreError` - 自定义错误类
- `ErrorHandler` - 错误处理器

## 许可证

MIT
