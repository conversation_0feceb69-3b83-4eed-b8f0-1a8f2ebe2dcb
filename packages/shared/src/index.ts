/**
 * @micro-core/shared
 * Micro-Core 共享工具包 - 公共基础设施层
 * 
 * @description 提供微前端项目的公共工具、类型定义、常量和错误处理机制
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 */

// 导出所有常量
export * from './constants';

// 导出所有类型定义
export * from './types';

// 导出所有工具函数
export * from './utils';

// 导出错误处理相关 - 显式导出以解决歧义
export {
    createAppError, createCommunicationError, createError, createPluginError, createResourceError, createRouteError, createSandboxError, createSystemError, ErrorHandler, globalErrorHandler, MicroCoreError
} from './errors';

// 版本信息
export const VERSION = '0.1.0';

// 项目元信息
export const PROJECT_INFO = {
    name: '@micro-core/shared',
    version: VERSION,
    description: 'Micro-Core 共享工具包 - 公共基础设施层',
    author: 'Echo <<EMAIL>>',
    license: 'MIT'
} as const;
