/**
 * Micro-Core 错误处理模块
 * @description 统一的错误处理机制，作为公共基础设施层
 * @version 0.1.0
 */

import { ERROR_CODES } from '../constants';
import type { ErrorCode, MicroCoreError as IMicroCoreError } from '../types';

/**
 * 微前端核心错误类
 * @description 提供统一的错误处理机制
 */
export class MicroCoreError extends Error implements IMicroCoreError {
    /** 错误码 */
    public readonly code: ErrorCode;
    /** 关联应用 */
    public readonly app?: string;
    /** 错误数据 */
    public readonly data?: Record<string, any>;
    /** 原始错误 */
    public readonly cause?: Error;

    constructor(
        code: ErrorCode,
        message: string,
        app?: string,
        data?: Record<string, any>,
        cause?: Error
    ) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code;
        this.app = app;
        this.data = data;
        this.cause = cause;

        // 保持错误堆栈
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 从错误码创建错误
     * @param code 错误码
     * @param app 关联应用
     * @param data 错误数据
     * @param cause 原始错误
     * @returns 微前端错误实例
     */
    static fromCode(
        code: ErrorCode,
        app?: string,
        data?: Record<string, any>,
        cause?: Error
    ): MicroCoreError {
        const message = ERROR_MESSAGES[code] || '未知错误';
        return new MicroCoreError(code, message, app, data, cause);
    }

    /**
     * 从普通错误创建微前端错误
     * @param error 普通错误
     * @param code 错误码
     * @param app 关联应用
     * @param data 错误数据
     * @returns 微前端错误实例
     */
    static fromError(
        error: Error,
        code: ErrorCode = ERROR_CODES.SYSTEM_ERROR,
        app?: string,
        data?: Record<string, any>
    ): MicroCoreError {
        return new MicroCoreError(code, error.message, app, data, error);
    }

    /**
     * 检查是否为微前端错误
     * @param error 错误对象
     * @returns 是否为微前端错误
     */
    static isMicroCoreError(error: any): error is MicroCoreError {
        return error instanceof MicroCoreError ||
            (error && error.name === 'MicroCoreError' && typeof error.code === 'number');
    }

    /**
     * 序列化错误
     * @returns 序列化后的错误对象
     */
    toJSON(): Record<string, any> {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            app: this.app,
            data: this.data,
            stack: this.stack,
            cause: this.cause ? {
                name: this.cause.name,
                message: this.cause.message,
                stack: this.cause.stack
            } : undefined
        };
    }

    /**
     * 格式化错误信息
     * @returns 格式化后的错误信息
     */
    format(): string {
        let formatted = `[错误 ${this.code}] ${this.message}`;

        if (this.app) {
            formatted += ` (应用: ${this.app})`;
        }

        if (this.data && Object.keys(this.data).length > 0) {
            formatted += ` (数据: ${JSON.stringify(this.data)})`;
        }

        return formatted;
    }
}

/**
 * 错误消息映射表
 */
const ERROR_MESSAGES: Record<number, string> = {
    // 系统错误 1000-1999
    [ERROR_CODES.SYSTEM_ERROR]: '系统错误',
    [ERROR_CODES.SYSTEM_INIT_FAILED]: '系统初始化失败',
    [ERROR_CODES.SYSTEM_START_FAILED]: '系统启动失败',
    [ERROR_CODES.SYSTEM_STOP_FAILED]: '系统停止失败',

    // 应用错误 2000-2999
    [ERROR_CODES.APP_LOAD_ERROR]: '应用加载错误',
    [ERROR_CODES.APP_LOAD_FAILED]: '应用加载失败',
    [ERROR_CODES.APP_BOOTSTRAP_FAILED]: '应用引导失败',
    [ERROR_CODES.APP_MOUNT_ERROR]: '应用挂载错误',
    [ERROR_CODES.APP_MOUNT_FAILED]: '应用挂载失败',
    [ERROR_CODES.APP_UNMOUNT_FAILED]: '应用卸载失败',
    [ERROR_CODES.APP_UPDATE_FAILED]: '应用更新失败',
    [ERROR_CODES.APP_NOT_FOUND]: '应用未找到',
    [ERROR_CODES.APP_ALREADY_REGISTERED]: '应用已注册',
    [ERROR_CODES.APP_CONFIG_INVALID]: '应用配置无效',

    // 沙箱错误 3000-3999
    [ERROR_CODES.SANDBOX_ERROR]: '沙箱错误',
    [ERROR_CODES.SANDBOX_CREATE_FAILED]: '沙箱创建失败',
    [ERROR_CODES.SANDBOX_ACTIVATE_FAILED]: '沙箱激活失败',
    [ERROR_CODES.SANDBOX_DEACTIVATE_FAILED]: '沙箱停用失败',
    [ERROR_CODES.SANDBOX_DESTROY_FAILED]: '沙箱销毁失败',
    [ERROR_CODES.SANDBOX_EXEC_FAILED]: '沙箱代码执行失败',
    [ERROR_CODES.SANDBOX_TYPE_UNSUPPORTED]: '不支持的沙箱类型',

    // 插件错误 4000-4999
    [ERROR_CODES.PLUGIN_ERROR]: '插件错误',
    [ERROR_CODES.PLUGIN_INSTALL_FAILED]: '插件安装失败',
    [ERROR_CODES.PLUGIN_UNINSTALL_FAILED]: '插件卸载失败',
    [ERROR_CODES.PLUGIN_NOT_FOUND]: '插件未找到',
    [ERROR_CODES.PLUGIN_ALREADY_INSTALLED]: '插件已安装',
    [ERROR_CODES.PLUGIN_CONFIG_INVALID]: '插件配置无效',

    // 路由错误 5000-5999
    [ERROR_CODES.ROUTE_ERROR]: '路由错误',
    [ERROR_CODES.ROUTE_MATCH_FAILED]: '路由匹配失败',
    [ERROR_CODES.ROUTE_NAVIGATE_FAILED]: '路由导航失败',
    [ERROR_CODES.ROUTE_CONFIG_INVALID]: '路由配置无效',

    // 资源错误 6000-6999
    [ERROR_CODES.RESOURCE_LOAD_FAILED]: '资源加载失败',
    [ERROR_CODES.RESOURCE_PARSE_FAILED]: '资源解析失败',
    [ERROR_CODES.RESOURCE_NOT_FOUND]: '资源未找到',

    // 通信错误 7000-7999
    [ERROR_CODES.COMMUNICATION_FAILED]: '通信失败',
    [ERROR_CODES.MESSAGE_SEND_FAILED]: '消息发送失败',
    [ERROR_CODES.MESSAGE_RECEIVE_FAILED]: '消息接收失败'
};

/**
 * 错误处理器类
 * @description 提供全局错误处理能力
 */
export class ErrorHandler {
    private static instance: ErrorHandler;
    private errorListeners: Array<(error: MicroCoreError) => void> = [];
    private errorFilters: Array<(error: MicroCoreError) => boolean> = [];
    private errorReporters: Array<(error: MicroCoreError) => Promise<void>> = [];

    private constructor() {
        this.setupGlobalErrorHandling();
    }

    /**
     * 获取错误处理器单例
     * @returns 错误处理器实例
     */
    static getInstance(): ErrorHandler {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }

    /**
     * 处理错误
     * @param error 错误对象
     */
    handle(error: Error | MicroCoreError): void {
        const microError = MicroCoreError.isMicroCoreError(error)
            ? error
            : MicroCoreError.fromError(error);

        // 应用错误过滤器
        const shouldHandle = this.errorFilters.length === 0 ||
            this.errorFilters.some(filter => filter(microError));

        if (!shouldHandle) {
            return;
        }

        // 调用错误监听器
        this.errorListeners.forEach(listener => {
            try {
                listener(microError);
            } catch (listenerError) {
                console.error('错误监听器执行失败:', listenerError);
            }
        });

        // 上报错误
        this.reportError(microError);

        // 控制台输出
        this.logError(microError);
    }

    /**
     * 添加错误监听器
     * @param listener 错误监听器
     */
    addErrorListener(listener: (error: MicroCoreError) => void): void {
        this.errorListeners.push(listener);
    }

    /**
     * 移除错误监听器
     * @param listener 错误监听器
     */
    removeErrorListener(listener: (error: MicroCoreError) => void): void {
        const index = this.errorListeners.indexOf(listener);
        if (index > -1) {
            this.errorListeners.splice(index, 1);
        }
    }

    /**
     * 添加错误过滤器
     * @param filter 错误过滤器
     */
    addErrorFilter(filter: (error: MicroCoreError) => boolean): void {
        this.errorFilters.push(filter);
    }

    /**
     * 移除错误过滤器
     * @param filter 错误过滤器
     */
    removeErrorFilter(filter: (error: MicroCoreError) => boolean): void {
        const index = this.errorFilters.indexOf(filter);
        if (index > -1) {
            this.errorFilters.splice(index, 1);
        }
    }

    /**
     * 添加错误上报器
     * @param reporter 错误上报器
     */
    addErrorReporter(reporter: (error: MicroCoreError) => Promise<void>): void {
        this.errorReporters.push(reporter);
    }

    /**
     * 移除错误上报器
     * @param reporter 错误上报器
     */
    removeErrorReporter(reporter: (error: MicroCoreError) => Promise<void>): void {
        const index = this.errorReporters.indexOf(reporter);
        if (index > -1) {
            this.errorReporters.splice(index, 1);
        }
    }

    /**
     * 设置全局错误处理
     */
    private setupGlobalErrorHandling(): void {
        // 处理未捕获的错误
        window.addEventListener('error', (event) => {
            this.handle(event.error || new Error(event.message));
        });

        // 处理未捕获的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handle(event.reason instanceof Error ? event.reason : new Error(String(event.reason)));
        });
    }

    /**
     * 上报错误
     * @param error 微前端错误
     */
    private async reportError(error: MicroCoreError): Promise<void> {
        for (const reporter of this.errorReporters) {
            try {
                await reporter(error);
            } catch (reportError) {
                console.error('错误上报失败:', reportError);
            }
        }
    }

    /**
     * 记录错误日志
     * @param error 微前端错误
     */
    private logError(error: MicroCoreError): void {
        const formatted = error.format();

        // 根据错误码确定日志级别
        if (error.code >= 1000 && error.code < 2000) {
            // 系统错误 - 严重
            console.error(formatted, error);
        } else if (error.code >= 2000 && error.code < 3000) {
            // 应用错误 - 警告
            console.warn(formatted, error);
        } else {
            // 其他错误 - 信息
            console.info(formatted, error);
        }
    }
}

/**
 * 创建微前端错误的便捷函数
 * @param code 错误码
 * @param message 错误信息
 * @param app 关联应用
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createError(
    code: ErrorCode,
    message: string,
    app?: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return new MicroCoreError(code, message, app, data, cause);
}

/**
 * 创建系统错误
 * @param message 错误信息
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createSystemError(
    message: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.SYSTEM_ERROR, message, undefined, data, cause);
}

/**
 * 创建应用错误
 * @param message 错误信息
 * @param app 关联应用
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createAppError(
    message: string,
    app: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.APP_LOAD_ERROR, message, app, data, cause);
}

/**
 * 创建沙箱错误
 * @param message 错误信息
 * @param app 关联应用
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createSandboxError(
    message: string,
    app?: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.SANDBOX_ERROR, message, app, data, cause);
}

/**
 * 创建插件错误
 * @param message 错误信息
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createPluginError(
    message: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.PLUGIN_ERROR, message, undefined, data, cause);
}

/**
 * 创建路由错误
 * @param message 错误信息
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createRouteError(
    message: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.ROUTE_ERROR, message, undefined, data, cause);
}

/**
 * 创建资源错误
 * @param message 错误信息
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createResourceError(
    message: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.RESOURCE_LOAD_FAILED, message, undefined, data, cause);
}

/**
 * 创建通信错误
 * @param message 错误信息
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误实例
 */
export function createCommunicationError(
    message: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    return createError(ERROR_CODES.COMMUNICATION_FAILED, message, undefined, data, cause);
}

// 导出全局错误处理器实例
export const globalErrorHandler = ErrorHandler.getInstance();

// 默认导出
export default MicroCoreError;