/**
 * Micro-Core 工具函数集合
 * @description 提供项目通用的工具函数，作为公共基础设施层
 * @version 0.1.0
 */

import { ERROR_CODES } from '../constants';
import type { AppConfig, MicroCoreError, PerformanceMetric } from '../types';

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID
 */
export function generateId(prefix = 'micro'): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}`;
}

/**
 * 验证URL是否有效
 * @param url URL字符串
 * @returns 是否有效
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 验证应用配置
 * @param config 应用配置
 * @returns 验证结果
 */
export function validateAppConfig(config: AppConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.name || typeof config.name !== 'string') {
        errors.push('应用名称必须是非空字符串');
    }

    if (!config.entry || typeof config.entry !== 'string') {
        errors.push('应用入口必须是非空字符串');
    } else if (!isValidUrl(config.entry)) {
        errors.push('应用入口必须是有效的URL');
    }

    if (!config.container) {
        errors.push('挂载容器不能为空');
    }

    if (!config.activeWhen) {
        errors.push('激活条件不能为空');
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 深度合并对象
 * @param target 目标对象
 * @param sources 源对象
 * @returns 合并后的对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();

    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} });
                deepMerge(target[key], source[key]);
            } else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }

    return deepMerge(target, ...sources);
}

/**
 * 判断是否为对象
 * @param item 待判断项
 * @returns 是否为对象
 */
export function isObject(item: any): item is Record<string, any> {
    return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * 深度克隆对象
 * @param obj 待克隆对象
 * @returns 克隆后的对象
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as T;
    }

    if (obj instanceof Array) {
        return obj.map(item => deepClone(item)) as T;
    }

    if (typeof obj === 'object') {
        const cloned = {} as T;
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }

    return obj;
}

/**
 * 防抖函数
 * @param fn 待防抖的函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number
): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => fn(...args), delay);
    };
}

/**
 * 节流函数
 * @param fn 待节流的函数
 * @param delay 延迟时间
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
    fn: T,
    delay: number
): (...args: Parameters<T>) => void {
    let lastCall = 0;
    return (...args: Parameters<T>) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            fn(...args);
        }
    };
}

/**
 * 异步重试函数
 * @param fn 待重试的异步函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟
 * @returns Promise
 */
export async function retry<T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;
            if (i < maxRetries) {
                await sleep(delay * Math.pow(2, i)); // 指数退避
            }
        }
    }

    throw lastError!;
}

/**
 * 睡眠函数
 * @param ms 毫秒数
 * @returns Promise
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 超时包装函数
 * @param promise 待包装的Promise
 * @param timeout 超时时间
 * @param errorMessage 超时错误信息
 * @returns Promise
 */
export function withTimeout<T>(
    promise: Promise<T>,
    timeout: number,
    errorMessage = '操作超时'
): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error(errorMessage)), timeout);
        })
    ]);
}

/**
 * 格式化错误信息
 * @param error 错误对象
 * @returns 格式化后的错误信息
 */
export function formatError(error: Error | MicroCoreError): string {
    if ('code' in error && error.code) {
        return `[错误 ${error.code}] ${error.message}`;
    }
    return error.message || '未知错误';
}

/**
 * 创建微前端错误
 * @param code 错误码
 * @param message 错误信息
 * @param app 关联应用
 * @param data 错误数据
 * @param cause 原始错误
 * @returns 微前端错误
 */
export function createMicroCoreError(
    code: number,
    message: string,
    app?: string,
    data?: Record<string, any>,
    cause?: Error
): MicroCoreError {
    const error = new Error(message) as MicroCoreError;
    error.name = 'MicroCoreError';
    error.code = code as any;
    error.app = app;
    error.data = data;
    error.cause = cause;
    return error;
}

/**
 * 获取容器元素
 * @param container 容器选择器或元素
 * @returns 容器元素
 */
export function getContainer(container: string | HTMLElement): HTMLElement {
    if (typeof container === 'string') {
        const element = document.querySelector(container);
        if (!element) {
            throw createMicroCoreError(
                ERROR_CODES.APP_CONFIG_INVALID,
                `容器元素未找到: ${container}`
            );
        }
        return element as HTMLElement;
    }
    return container;
}

/**
 * 加载脚本
 * @param src 脚本地址
 * @param options 加载选项
 * @returns Promise
 */
export function loadScript(
    src: string,
    options: {
        async?: boolean;
        defer?: boolean;
        crossOrigin?: string;
        integrity?: string;
        timeout?: number;
    } = {}
): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.async = options.async ?? true;
        script.defer = options.defer ?? false;

        if (options.crossOrigin) {
            script.crossOrigin = options.crossOrigin;
        }

        if (options.integrity) {
            script.integrity = options.integrity;
        }

        const timeout = options.timeout || 30000;
        const timeoutId = setTimeout(() => {
            reject(createMicroCoreError(
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                `脚本加载超时: ${src}`
            ));
        }, timeout);

        script.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        script.onerror = () => {
            clearTimeout(timeoutId);
            reject(createMicroCoreError(
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                `脚本加载失败: ${src}`
            ));
        };

        document.head.appendChild(script);
    });
}

/**
 * 加载样式
 * @param href 样式地址
 * @param options 加载选项
 * @returns Promise
 */
export function loadStyle(
    href: string,
    options: {
        crossOrigin?: string;
        integrity?: string;
        timeout?: number;
    } = {}
): Promise<void> {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;

        if (options.crossOrigin) {
            link.crossOrigin = options.crossOrigin;
        }

        if (options.integrity) {
            link.integrity = options.integrity;
        }

        const timeout = options.timeout || 30000;
        const timeoutId = setTimeout(() => {
            reject(createMicroCoreError(
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                `样式加载超时: ${href}`
            ));
        }, timeout);

        link.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        link.onerror = () => {
            clearTimeout(timeoutId);
            reject(createMicroCoreError(
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                `样式加载失败: ${href}`
            ));
        };

        document.head.appendChild(link);
    });
}

/**
 * 获取资源内容
 * @param url 资源地址
 * @param options 请求选项
 * @returns Promise
 */
export async function fetchResource(
    url: string,
    options: RequestInit = {}
): Promise<string> {
    try {
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'text/plain',
                ...options.headers
            }
        });

        if (!response.ok) {
            throw createMicroCoreError(
                ERROR_CODES.RESOURCE_LOAD_FAILED,
                `资源请求失败: ${response.status} ${response.statusText}`
            );
        }

        return await response.text();
    } catch (error) {
        if (error instanceof Error && error.name === 'MicroCoreError') {
            throw error;
        }
        throw createMicroCoreError(
            ERROR_CODES.RESOURCE_LOAD_FAILED,
            `资源加载失败: ${url}`,
            undefined,
            undefined,
            error as Error
        );
    }
}

/**
 * 解析HTML内容
 * @param html HTML字符串
 * @returns 解析后的文档片段
 */
export function parseHTML(html: string): DocumentFragment {
    const template = document.createElement('template');
    template.innerHTML = html.trim();
    return template.content;
}

/**
 * 提取脚本标签
 * @param html HTML字符串
 * @returns 脚本信息数组
 */
export function extractScripts(html: string): Array<{
    src?: string;
    content?: string;
    async?: boolean;
    defer?: boolean;
}> {
    const scripts: Array<{
        src?: string;
        content?: string;
        async?: boolean;
        defer?: boolean;
    }> = [];

    const fragment = parseHTML(html);
    const scriptElements = fragment.querySelectorAll('script');

    scriptElements.forEach(script => {
        scripts.push({
            src: script.src || undefined,
            content: script.textContent || undefined,
            async: script.async,
            defer: script.defer
        });
    });

    return scripts;
}

/**
 * 提取样式标签
 * @param html HTML字符串
 * @returns 样式信息数组
 */
export function extractStyles(html: string): Array<{
    href?: string;
    content?: string;
}> {
    const styles: Array<{
        href?: string;
        content?: string;
    }> = [];

    const fragment = parseHTML(html);
    const linkElements = fragment.querySelectorAll('link[rel="stylesheet"]');
    const styleElements = fragment.querySelectorAll('style');

    linkElements.forEach(link => {
        styles.push({
            href: (link as HTMLLinkElement).href || undefined
        });
    });

    styleElements.forEach(style => {
        styles.push({
            content: style.textContent || undefined
        });
    });

    return styles;
}

/**
 * 创建简单日志器
 * @param name 日志器名称
 * @returns 日志器对象
 */
export function createLogger(name: string) {
    const prefix = `[${name}]`;

    return {
        debug: (message: string, ...args: any[]) => {
            console.debug(prefix, message, ...args);
        },
        info: (message: string, ...args: any[]) => {
            console.info(prefix, message, ...args);
        },
        warn: (message: string, ...args: any[]) => {
            console.warn(prefix, message, ...args);
        },
        error: (message: string, ...args: any[]) => {
            console.error(prefix, message, ...args);
        }
    };
}

/**
 * 性能测量工具
 * @param name 测量名称
 * @returns 停止测量的函数
 */
export function measurePerformance(name: string): () => PerformanceMetric {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;

    return () => {
        const endTime = performance.now();
        const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
        const duration = endTime - startTime;
        const memoryDelta = endMemory - startMemory;

        return {
            name,
            value: duration,
            unit: 'ms',
            timestamp: Date.now(),
            tags: {
                memoryDelta: memoryDelta.toString()
            }
        };
    };
}

/**
 * 检查浏览器兼容性
 * @returns 兼容性检查结果
 */
export function checkBrowserCompatibility(): {
    compatible: boolean;
    features: Record<string, boolean>;
    warnings: string[];
} {
    const features = {
        proxy: typeof Proxy !== 'undefined',
        customElements: typeof customElements !== 'undefined',
        shadowDOM: typeof ShadowRoot !== 'undefined',
        es6Modules: typeof Symbol !== 'undefined',
        fetch: typeof fetch !== 'undefined',
        promise: typeof Promise !== 'undefined',
        weakMap: typeof WeakMap !== 'undefined',
        mutationObserver: typeof MutationObserver !== 'undefined'
    };

    const warnings: string[] = [];
    const requiredFeatures = ['proxy', 'fetch', 'promise'];

    requiredFeatures.forEach(feature => {
        if (!features[feature as keyof typeof features]) {
            warnings.push(`缺少必需特性: ${feature}`);
        }
    });

    return {
        compatible: warnings.length === 0,
        features,
        warnings
    };
}

/**
 * 获取当前环境信息
 * @returns 环境信息
 */
export function getEnvironmentInfo(): {
    userAgent: string;
    platform: string;
    language: string;
    cookieEnabled: boolean;
    onLine: boolean;
    screen: {
        width: number;
        height: number;
        colorDepth: number;
    };
} {
    return {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        screen: {
            width: screen.width,
            height: screen.height,
            colorDepth: screen.colorDepth
        }
    };
}