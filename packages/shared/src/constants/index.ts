/**
 * @fileoverview Micro-Core 常量定义
 * @description 集中管理所有项目常量，确保一致性
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 应用状态常量
export const APP_STATUS = {
    NOT_LOADED: 'NOT_LOADED',
    LOADING_SOURCE_CODE: 'LOADING_SOURCE_CODE',
    NOT_BOOTSTRAPPED: 'NOT_BOOTSTRAPPED',
    BOOTSTRAPPING: 'BOOTSTRAPPING',
    NOT_MOUNTED: 'NOT_MOUNTED',
    MOUNTING: 'MOUNTING',
    MOUNTED: 'MOUNTED',
    UPDATING: 'UPDATING',
    UNMOUNTING: 'UNMOUNTING',
    UNLOADING: 'UNLOADING',
    LOAD_ERROR: 'LOAD_ERROR',
    SKIP_BECAUSE_BROKEN: 'SKIP_BECAUSE_BROKEN'
} as const;

// 沙箱类型常量
export const SANDBOX_TYPES = {
    PROXY: 'proxy',
    DEFINE_PROPERTY: 'defineProperty',
    WEB_COMPONENT: 'webComponent',
    IFRAME: 'iframe',
    NAMESPACE: 'namespace',
    FEDERATION: 'federation'
} as const;

// 生命周期钩子常量
export const LIFECYCLE_HOOKS = {
    BEFORE_LOAD: 'beforeLoad',
    LOADED: 'loaded',
    BEFORE_BOOTSTRAP: 'beforeBootstrap',
    BOOTSTRAPPED: 'bootstrapped',
    BEFORE_MOUNT: 'beforeMount',
    MOUNTED: 'mounted',
    BEFORE_UPDATE: 'beforeUpdate',
    UPDATED: 'updated',
    BEFORE_UNMOUNT: 'beforeUnmount',
    UNMOUNTED: 'unmounted',
    BEFORE_UNLOAD: 'beforeUnload',
    UNLOADED: 'unloaded',
    ERROR: 'error'
} as const;

// 事件类型常量
export const EVENT_TYPES = {
    // 应用事件
    APP_LOADED: 'app:loaded',
    APP_MOUNTED: 'app:mounted',
    APP_UNMOUNTED: 'app:unmounted',
    APP_ERROR: 'app:error',

    // 路由事件
    ROUTE_CHANGED: 'route:changed',
    ROUTE_BEFORE_CHANGE: 'route:beforeChange',
    ROUTE_ERROR: 'route:error',

    // 通信事件
    MESSAGE_SENT: 'message:sent',
    MESSAGE_RECEIVED: 'message:received',
    MESSAGE_ERROR: 'message:error',

    // 系统事件
    SYSTEM_READY: 'system:ready',
    SYSTEM_ERROR: 'system:error',
    SYSTEM_SHUTDOWN: 'system:shutdown'
} as const;

// 错误码常量
export const ERROR_CODES = {
    // 系统错误 (1000-1999)
    SYSTEM_INIT_FAILED: 1001,
    SYSTEM_NOT_READY: 1002,
    SYSTEM_ALREADY_STARTED: 1003,
    SYSTEM_SHUTDOWN_FAILED: 1004,

    // 应用错误 (2000-2999)
    APP_NOT_FOUND: 2001,
    APP_LOAD_FAILED: 2002,
    APP_BOOTSTRAP_FAILED: 2003,
    APP_MOUNT_FAILED: 2004,
    APP_UNMOUNT_FAILED: 2005,
    APP_UPDATE_FAILED: 2006,
    APP_ALREADY_REGISTERED: 2007,
    APP_INVALID_CONFIG: 2008,

    // 沙箱错误 (3000-3999)
    SANDBOX_CREATE_FAILED: 3001,
    SANDBOX_ACTIVATE_FAILED: 3002,
    SANDBOX_DEACTIVATE_FAILED: 3003,
    SANDBOX_EXEC_FAILED: 3004,
    SANDBOX_TYPE_NOT_SUPPORTED: 3005,

    // 插件错误 (4000-4999)
    PLUGIN_NOT_FOUND: 4001,
    PLUGIN_INSTALL_FAILED: 4002,
    PLUGIN_UNINSTALL_FAILED: 4003,
    PLUGIN_INVALID_CONFIG: 4004,
    PLUGIN_DEPENDENCY_MISSING: 4005,

    // 路由错误 (5000-5999)
    ROUTE_NOT_FOUND: 5001,
    ROUTE_NAVIGATE_FAILED: 5002,
    ROUTE_INVALID_CONFIG: 5003,
    ROUTE_GUARD_REJECTED: 5004,

    // 通信错误 (6000-6999)
    COMMUNICATION_SEND_FAILED: 6001,
    COMMUNICATION_RECEIVE_FAILED: 6002,
    COMMUNICATION_TIMEOUT: 6003,
    COMMUNICATION_INVALID_MESSAGE: 6004,

    // 资源错误 (7000-7999)
    RESOURCE_LOAD_FAILED: 7001,
    RESOURCE_NOT_FOUND: 7002,
    RESOURCE_INVALID_TYPE: 7003,
    RESOURCE_CACHE_FAILED: 7004
} as const;

// 资源类型常量
export const RESOURCE_TYPES = {
    SCRIPT: 'script',
    STYLE: 'style',
    HTML: 'html',
    JSON: 'json',
    IMAGE: 'image',
    FONT: 'font',
    OTHER: 'other'
} as const;

// 插件类型常量
export const PLUGIN_TYPES = {
    CORE: 'core',
    SANDBOX: 'sandbox',
    ROUTER: 'router',
    COMMUNICATION: 'communication',
    AUTH: 'auth',
    LOGGER: 'logger',
    METRICS: 'metrics',
    DEVTOOLS: 'devtools',
    LOADER: 'loader',
    PREFETCH: 'prefetch',
    COMPAT: 'compat'
} as const;

// 框架类型常量
export const FRAMEWORK_TYPES = {
    REACT: 'react',
    VUE2: 'vue2',
    VUE3: 'vue3',
    ANGULAR: 'angular',
    SVELTE: 'svelte',
    SOLID: 'solid',
    HTML: 'html',
    VANILLA: 'vanilla'
} as const;

// 构建工具类型常量
export const BUILDER_TYPES = {
    VITE: 'vite',
    WEBPACK: 'webpack',
    ROLLUP: 'rollup',
    ESBUILD: 'esbuild',
    PARCEL: 'parcel',
    RSPACK: 'rspack',
    TURBOPACK: 'turbopack'
} as const;

// 性能阈值常量
export const PERFORMANCE_THRESHOLDS = {
    APP_LOAD_TIME: 500, // ms
    APP_MOUNT_TIME: 100, // ms
    ROUTE_CHANGE_TIME: 50, // ms
    MEMORY_USAGE_LIMIT: 50 * 1024 * 1024, // 50MB
    BUNDLE_SIZE_LIMIT: 1024 * 1024 // 1MB
} as const;

// 缓存配置常量
export const CACHE_CONFIG = {
    DEFAULT_TTL: 5 * 60 * 1000, // 5分钟
    MAX_SIZE: 100, // 最大缓存条目数
    STORAGE_KEY_PREFIX: 'micro-core:'
} as const;

// 开发模式常量
export const DEV_MODE = {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
    TEST: 'test'
} as const;

// 日志级别常量
export const LOG_LEVELS = {
    ERROR: 0,
    WARN: 1,
    INFO: 2,
    DEBUG: 3,
    TRACE: 4
} as const;

// 导出类型
export type AppStatus = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type LifecycleHook = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
export type PluginType = typeof PLUGIN_TYPES[keyof typeof PLUGIN_TYPES];
export type FrameworkType = typeof FRAMEWORK_TYPES[keyof typeof FRAMEWORK_TYPES];
export type BuilderType = typeof BUILDER_TYPES[keyof typeof BUILDER_TYPES];
export type LogLevel = typeof LOG_LEVELS[keyof typeof LOG_LEVELS];