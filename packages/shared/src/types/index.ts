/**
 * Micro-Core 核心类型定义
 * @description 统一管理所有类型，作为项目的公共基础设施层
 * @version 0.1.0
 */

import type {
    ADAPTER_TYPES,
    APP_STATUS,
    BUILDER_TYPES,
    ERROR_CODES,
    EVENT_TYPES,
    LIFECYCLE_HOOKS,
    MESSAGE_TYPES,
    PLUGIN_TYPES,
    RESOURCE_TYPES,
    SANDBOX_TYPES
} from '../constants';

// 基础类型
export type AppStatus = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type LifecycleHook = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];
export type AdapterType = typeof ADAPTER_TYPES[keyof typeof ADAPTER_TYPES];
export type BuilderType = typeof BUILDER_TYPES[keyof typeof BUILDER_TYPES];
export type PluginType = typeof PLUGIN_TYPES[keyof typeof PLUGIN_TYPES];
export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
export type MessageType = typeof MESSAGE_TYPES[keyof typeof MESSAGE_TYPES];

// 工具类型
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Awaitable<T> = T | Promise<T>;

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

export type OptionalKeys<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

// 应用相关类型
export interface AppConfig {
    /** 应用名称，必须唯一 */
    name: string;
    /** 应用入口地址 */
    entry: string;
    /** 挂载容器 */
    container: string | HTMLElement;
    /** 激活条件 */
    activeWhen: string | string[] | ((location: Location) => boolean);
    /** 应用属性 */
    props?: Record<string, any>;
    /** 沙箱配置 */
    sandbox?: SandboxConfig;
    /** 适配器类型 */
    adapter?: AdapterType;
    /** 生命周期钩子 */
    lifecycle?: Partial<LifecycleHooks>;
    /** 自定义加载器 */
    loader?: AppLoader;
    /** 预加载配置 */
    prefetch?: boolean | PrefetchConfig;
    /** 错误边界配置 */
    errorBoundary?: ErrorBoundaryConfig;
}

export interface SandboxConfig {
    /** 沙箱类型 */
    type: SandboxType;
    /** 严格模式 */
    strict?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
    /** 自定义沙箱选项 */
    options?: Record<string, any>;
}

export interface PrefetchConfig {
    /** 预加载时机 */
    timing: 'idle' | 'load' | 'domready';
    /** 预加载资源类型 */
    resources?: ResourceType[];
    /** 预加载优先级 */
    priority?: 'high' | 'normal' | 'low';
}

export interface ErrorBoundaryConfig {
    /** 是否启用错误边界 */
    enabled: boolean;
    /** 错误回调 */
    onError?: (error: Error, app: AppInstance) => void;
    /** 错误恢复策略 */
    recovery?: 'reload' | 'fallback' | 'none';
    /** 降级组件 */
    fallback?: () => HTMLElement;
}

// 应用实例类型
export interface AppInstance {
    /** 应用名称 */
    name: string;
    /** 应用状态 */
    status: AppStatus;
    /** 应用配置 */
    config: AppConfig;
    /** 沙箱实例 */
    sandbox?: SandboxInstance;
    /** 适配器实例 */
    adapter?: FrameworkAdapter;
    /** 应用资源 */
    resources?: AppResource[];
    /** 挂载容器 */
    container?: HTMLElement;
    /** 应用属性 */
    props?: Record<string, any>;
    /** 创建时间 */
    createdAt: number;
    /** 最后更新时间 */
    updatedAt: number;
}

export interface AppResource {
    /** 资源类型 */
    type: ResourceType;
    /** 资源URL */
    url: string;
    /** 资源内容 */
    content?: string;
    /** 是否已加载 */
    loaded: boolean;
    /** 加载时间 */
    loadTime?: number;
    /** 资源大小 */
    size?: number;
}

// 沙箱相关类型
export interface SandboxInstance {
    /** 沙箱名称 */
    name: string;
    /** 沙箱类型 */
    type: SandboxType;
    /** 是否激活 */
    active: boolean;
    /** 沙箱上下文 */
    context?: SandboxContext;
    /** 激活沙箱 */
    activate(app: AppInstance): Promise<void>;
    /** 停用沙箱 */
    deactivate(): Promise<void>;
    /** 销毁沙箱 */
    destroy(): Promise<void>;
    /** 执行代码 */
    execScript(code: string, filename?: string): Promise<any>;
    /** 获取沙箱上下文 */
    getContext(): SandboxContext | undefined;
}

export interface SandboxContext {
    /** 沙箱名称 */
    name: string;
    /** 是否激活 */
    active: boolean;
    /** 沙箱窗口对象 */
    window: Window | Record<string, any>;
    /** 沙箱文档对象 */
    document: Document;
    /** 创建时间 */
    createdAt: number;
    /** 自定义属性 */
    [key: string]: any;
}

export interface SandboxOptions {
    /** 沙箱名称 */
    name: string;
    /** 严格模式 */
    strict?: boolean;
    /** 全局变量白名单 */
    globalWhitelist?: string[];
    /** 全局变量黑名单 */
    globalBlacklist?: string[];
    /** 自定义选项 */
    [key: string]: any;
}

// 插件相关类型
export interface Plugin {
    /** 插件名称 */
    name: string;
    /** 插件版本 */
    version: string;
    /** 插件类型 */
    type?: PluginType;
    /** 插件描述 */
    description?: string;
    /** 插件依赖 */
    dependencies?: string[];
    /** 安装插件 */
    install(kernel: MicroCoreKernel, options?: Record<string, any>): Awaitable<void>;
    /** 卸载插件 */
    uninstall?(kernel: MicroCoreKernel): Awaitable<void>;
    /** 插件配置 */
    configure?(options: Record<string, any>): void;
}

export interface PluginInstance {
    /** 插件信息 */
    plugin: Plugin;
    /** 插件选项 */
    options?: Record<string, any>;
    /** 是否已安装 */
    installed: boolean;
    /** 安装时间 */
    installedAt?: number;
}

// 框架适配器类型
export interface FrameworkAdapter {
    /** 适配器名称 */
    name: string;
    /** 适配器版本 */
    version: string;
    /** 适配器类型 */
    type: AdapterType;
    /** 引导应用 */
    bootstrap?(app: AppInstance): Awaitable<void>;
    /** 挂载应用 */
    mount(app: AppInstance, container: HTMLElement): Awaitable<void>;
    /** 卸载应用 */
    unmount(app: AppInstance, container: HTMLElement): Awaitable<void>;
    /** 更新应用 */
    update?(app: AppInstance, props: Record<string, any>): Awaitable<void>;
    /** 获取应用信息 */
    getAppInfo?(app: AppInstance): Record<string, any>;
}

// 内核相关类型
export interface MicroCoreKernel {
    /** 注册应用 */
    registerApp(config: AppConfig): Promise<void>;
    /** 注销应用 */
    unregisterApp(name: string): Promise<void>;
    /** 加载应用 */
    loadApp(name: string): Promise<void>;
    /** 挂载应用 */
    mountApp(name: string, container?: HTMLElement): Promise<void>;
    /** 卸载应用 */
    unmountApp(name: string): Promise<void>;
    /** 更新应用 */
    updateApp(name: string, props: Record<string, any>): Promise<void>;
    /** 获取应用 */
    getApp(name: string): AppInstance | undefined;
    /** 获取所有应用 */
    getApps(): AppInstance[];
    /** 使用插件 */
    use(plugin: Plugin, options?: Record<string, any>): Promise<void>;
    /** 卸载插件 */
    unuse(name: string): Promise<void>;
    /** 获取插件 */
    getPlugin(name: string): PluginInstance | undefined;
    /** 获取所有插件 */
    getPlugins(): PluginInstance[];
    /** 启动内核 */
    start(): Promise<void>;
    /** 停止内核 */
    stop(): Promise<void>;
    /** 获取事件总线 */
    getEventBus(): EventBus;
}

export interface MicroCoreOptions {
    /** 容器选择器或元素 */
    container?: string | HTMLElement;
    /** 运行模式 */
    mode?: 'development' | 'production' | 'sidecar';
    /** 默认沙箱配置 */
    sandbox?: SandboxConfig;
    /** 路由配置 */
    router?: RouterConfig;
    /** 预加载配置 */
    prefetch?: PrefetchConfig;
    /** 错误处理配置 */
    errorHandler?: ErrorHandlerConfig;
    /** 性能监控配置 */
    performance?: PerformanceConfig;
    /** 日志配置 */
    logger?: LoggerConfig;
}

// 生命周期相关类型
export type LifecycleFn<T = any> = (app: AppInstance) => Awaitable<T>;

export interface LifecycleHooks {
    beforeLoad?: LifecycleFn;
    afterLoad?: LifecycleFn;
    beforeBootstrap?: LifecycleFn;
    afterBootstrap?: LifecycleFn;
    beforeMount?: LifecycleFn;
    afterMount?: LifecycleFn;
    beforeUnmount?: LifecycleFn;
    afterUnmount?: LifecycleFn;
    beforeUpdate?: LifecycleFn;
    afterUpdate?: LifecycleFn;
}

export interface AppLoader {
    /** 加载应用资源 */
    load(config: AppConfig): Promise<AppResource[]>;
    /** 解析应用清单 */
    parseManifest?(entry: string): Promise<AppManifest>;
    /** 预加载资源 */
    prefetch?(resources: AppResource[]): Promise<void>;
}

export interface AppManifest {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 入口文件 */
    entry: string;
    /** 资源列表 */
    resources: AppResource[];
    /** 依赖列表 */
    dependencies?: string[];
    /** 应用元数据 */
    metadata?: Record<string, any>;
}

// 事件相关类型
export interface MicroCoreEvent {
    /** 事件类型 */
    type: EventType;
    /** 事件数据 */
    data?: any;
    /** 关联应用 */
    app?: AppInstance;
    /** 事件时间戳 */
    timestamp: number;
    /** 事件ID */
    id: string;
}

export interface EventBus {
    /** 监听事件 */
    on(type: EventType, listener: EventListener): void;
    /** 监听一次事件 */
    once(type: EventType, listener: EventListener): void;
    /** 取消监听 */
    off(type: EventType, listener?: EventListener): void;
    /** 触发事件 */
    emit(type: EventType, data?: any): void;
    /** 清除所有监听器 */
    clear(): void;
}

export type EventListener = (event: MicroCoreEvent) => void;

// 路由相关类型
export interface RouterConfig {
    /** 路由模式 */
    mode?: 'hash' | 'history' | 'memory';
    /** 基础路径 */
    base?: string;
    /** 路由规则 */
    routes?: RouteConfig[];
    /** 路由守卫 */
    guards?: RouteGuard[];
}

export interface RouteConfig {
    /** 路径模式 */
    path: string;
    /** 应用名称 */
    app: string;
    /** 路由名称 */
    name?: string;
    /** 路由元数据 */
    meta?: Record<string, any>;
    /** 子路由 */
    children?: RouteConfig[];
}

export interface RouteMatch {
    /** 匹配的路由 */
    route: RouteConfig;
    /** 路径参数 */
    params: Record<string, string>;
    /** 查询参数 */
    query: Record<string, string>;
    /** 路径 */
    path: string;
}

export interface RouteGuard {
    /** 守卫名称 */
    name: string;
    /** 守卫函数 */
    guard: (to: RouteMatch, from?: RouteMatch) => Awaitable<boolean | string>;
}

// 通信相关类型
export interface CommunicationMessage {
    /** 消息ID */
    id: string;
    /** 消息类型 */
    type: MessageType;
    /** 发送方 */
    from: string;
    /** 接收方 */
    to?: string;
    /** 消息数据 */
    data: any;
    /** 时间戳 */
    timestamp: number;
    /** 是否需要响应 */
    needResponse?: boolean;
}

export interface CommunicationChannel {
    /** 发送消息 */
    send(message: CommunicationMessage): Promise<void>;
    /** 监听消息 */
    listen(handler: MessageHandler): void;
    /** 停止监听 */
    unlisten(handler: MessageHandler): void;
    /** 关闭通道 */
    close(): void;
}

export type MessageHandler = (message: CommunicationMessage) => Awaitable<any>;

// 错误相关类型
export interface MicroCoreError extends Error {
    /** 错误码 */
    code: ErrorCode;
    /** 关联应用 */
    app?: string;
    /** 错误数据 */
    data?: Record<string, any>;
    /** 原始错误 */
    cause?: Error;
    /** 错误堆栈 */
    stack?: string;
}

export interface ErrorHandlerConfig {
    /** 是否启用全局错误处理 */
    enabled?: boolean;
    /** 错误回调 */
    onError?: (error: MicroCoreError) => void;
    /** 错误过滤器 */
    filter?: (error: MicroCoreError) => boolean;
    /** 错误上报 */
    reporter?: ErrorReporter;
}

export interface ErrorReporter {
    /** 上报错误 */
    report(error: MicroCoreError): Promise<void>;
}

// 性能监控类型
export interface PerformanceConfig {
    /** 是否启用性能监控 */
    enabled?: boolean;
    /** 性能指标收集器 */
    collectors?: PerformanceCollector[];
    /** 性能阈值 */
    thresholds?: PerformanceThresholds;
}

export interface PerformanceCollector {
    /** 收集器名称 */
    name: string;
    /** 收集性能数据 */
    collect(): PerformanceMetric[];
}

export interface PerformanceMetric {
    /** 指标名称 */
    name: string;
    /** 指标值 */
    value: number;
    /** 指标单位 */
    unit: string;
    /** 时间戳 */
    timestamp: number;
    /** 标签 */
    tags?: Record<string, string>;
}

export interface PerformanceThresholds {
    /** 应用加载时间阈值 */
    appLoadTime?: number;
    /** 应用挂载时间阈值 */
    appMountTime?: number;
    /** 内存使用阈值 */
    memoryUsage?: number;
}

// 日志相关类型
export interface LoggerConfig {
    /** 日志级别 */
    level?: 'debug' | 'info' | 'warn' | 'error';
    /** 日志输出器 */
    outputs?: LogOutput[];
    /** 日志格式化器 */
    formatter?: LogFormatter;
}

export interface LogOutput {
    /** 输出器名称 */
    name: string;
    /** 输出日志 */
    write(entry: LogEntry): void;
}

export interface LogFormatter {
    /** 格式化日志 */
    format(entry: LogEntry): string;
}

export interface LogEntry {
    /** 日志级别 */
    level: string;
    /** 日志消息 */
    message: string;
    /** 时间戳 */
    timestamp: number;
    /** 日志标签 */
    tags?: Record<string, string>;
    /** 额外数据 */
    data?: any;
}