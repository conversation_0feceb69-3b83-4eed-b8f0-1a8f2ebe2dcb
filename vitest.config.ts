import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        environment: 'jsdom',
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html'],
            thresholds: {
                global: {
                    branches: 80,
                    functions: 85,
                    lines: 85,
                    statements: 85
                }
            }
        },
        setupFiles: ['__tests__/setup.ts']
    }
});
