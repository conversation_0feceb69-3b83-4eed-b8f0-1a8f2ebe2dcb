/**
 * @fileoverview 统一构建配置
 * @description 为所有子包提供统一的Vite构建配置
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { resolve } from 'path';
import { type UserConfig } from 'vite';
import dts from 'vite-plugin-dts';

/**
 * 构建配置选项
 */
export interface BuildConfigOptions {
    /** 包名称 */
    name: string;
    /** 入口文件路径 */
    entry?: string;
    /** 输出格式 */
    formats?: ('es' | 'cjs' | 'umd')[];
    /** 外部依赖 */
    external?: string[];
    /** 全局变量映射 */
    globals?: Record<string, string>;
    /** 是否生成类型定义文件 */
    dts?: boolean;
    /** 是否生成sourcemap */
    sourcemap?: boolean;
    /** 是否压缩 */
    minify?: boolean | 'esbuild' | 'terser';
    /** 自定义配置 */
    customConfig?: Partial<UserConfig>;
}

/**
 * 默认外部依赖
 */
const DEFAULT_EXTERNAL = [
    'react',
    'react-dom',
    'react/jsx-runtime',
    'vue',
    '@vue/runtime-core',
    '@vue/runtime-dom',
    'angular',
    '@angular/core',
    '@angular/common',
    '@angular/platform-browser',
    '@angular/platform-browser-dynamic'
];

/**
 * 默认全局变量映射
 */
const DEFAULT_GLOBALS = {
    'react': 'React',
    'react-dom': 'ReactDOM',
    'react/jsx-runtime': 'ReactJSXRuntime',
    'vue': 'Vue',
    '@vue/runtime-core': 'VueRuntimeCore',
    '@vue/runtime-dom': 'VueRuntimeDOM',
    'angular': 'ng',
    '@angular/core': 'ng.core',
    '@angular/common': 'ng.common',
    '@angular/platform-browser': 'ng.platformBrowser',
    '@angular/platform-browser-dynamic': 'ng.platformBrowserDynamic'
};

/**
 * 创建构建配置
 * @param options 构建选项
 * @returns Vite配置
 */
export function createBuildConfig(options: BuildConfigOptions): UserConfig {
    const {
        name,
        entry = 'src/index.ts',
        formats = ['es', 'cjs'],
        external = [],
        globals = {},
        dts: enableDts = true,
        sourcemap = true,
        minify = 'esbuild',
        customConfig = {}
    } = options;

    // 合并外部依赖
    const allExternal = [...DEFAULT_EXTERNAL, ...external];

    // 合并全局变量映射
    const allGlobals = { ...DEFAULT_GLOBALS, ...globals };

    // 基础配置
    const baseConfig: UserConfig = {
        plugins: [
            // 生成类型定义文件
            ...(enableDts ? [
                dts({
                    insertTypesEntry: true,
                    rollupTypes: true,
                    exclude: [
                        '**/*.test.ts',
                        '**/*.test.tsx',
                        '**/*.spec.ts',
                        '**/*.spec.tsx',
                        '**/test/**',
                        '**/tests/**',
                        '**/__tests__/**'
                    ]
                })
            ] : [])
        ],
        build: {
            lib: {
                entry: resolve(process.cwd(), entry),
                name: toPascalCase(name),
                formats,
                fileName: (format) => {
                    switch (format) {
                        case 'es':
                            return 'index.mjs';
                        case 'cjs':
                            return 'index.js';
                        case 'umd':
                            return 'index.umd.js';
                        default:
                            return `index.${format}.js`;
                    }
                }
            },
            rollupOptions: {
                external: allExternal,
                output: {
                    globals: allGlobals,
                    // 确保导出名称一致
                    exports: 'named'
                }
            },
            sourcemap,
            minify,
            // 优化构建性能
            target: 'es2020',
            // 清理输出目录
            emptyOutDir: true
        },
        define: {
            __VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0'),
            __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production'),
            __PROD__: JSON.stringify(process.env.NODE_ENV === 'production')
        },
        // 优化依赖预构建
        optimizeDeps: {
            include: [],
            exclude: allExternal
        }
    };

    // 合并自定义配置
    return mergeConfig(baseConfig, customConfig);
}

/**
 * 创建开发模式配置
 * @param options 构建选项
 * @returns Vite配置
 */
export function createDevConfig(options: BuildConfigOptions): UserConfig {
    const baseConfig = createBuildConfig(options);

    return mergeConfig(baseConfig, {
        build: {
            ...baseConfig.build,
            minify: false,
            sourcemap: true,
            watch: {}
        },
        define: {
            ...baseConfig.define,
            __DEV__: JSON.stringify(true),
            __PROD__: JSON.stringify(false)
        }
    });
}

/**
 * 创建生产模式配置
 * @param options 构建选项
 * @returns Vite配置
 */
export function createProdConfig(options: BuildConfigOptions): UserConfig {
    const baseConfig = createBuildConfig(options);

    return mergeConfig(baseConfig, {
        build: {
            ...baseConfig.build,
            minify: 'esbuild',
            sourcemap: false
        },
        define: {
            ...baseConfig.define,
            __DEV__: JSON.stringify(false),
            __PROD__: JSON.stringify(true)
        }
    });
}

/**
 * 将字符串转换为PascalCase
 * @param str 输入字符串
 * @returns PascalCase字符串
 */
function toPascalCase(str: string): string {
    return str
        .replace(/[@\/\-_]/g, ' ')
        .replace(/\b\w/g, (char) => char.toUpperCase())
        .replace(/\s/g, '');
}

/**
 * 深度合并配置对象
 * @param target 目标对象
 * @param source 源对象
 * @returns 合并后的对象
 */
function mergeConfig(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = mergeConfig(target[key] || {}, source[key]);
        } else {
            result[key] = source[key];
        }
    }

    return result;
}

/**
 * 预设配置
 */
export const presets = {
    /**
     * 核心包配置
     */
    core: (customConfig?: Partial<BuildConfigOptions>) => createBuildConfig({
        name: '@micro-core/core',
        ...customConfig
    }),

    /**
     * 共享包配置
     */
    shared: (customConfig?: Partial<BuildConfigOptions>) => createBuildConfig({
        name: '@micro-core/shared',
        external: [], // shared包不排除任何依赖
        ...customConfig
    }),

    /**
     * 插件包配置
     */
    plugin: (name: string, customConfig?: Partial<BuildConfigOptions>) => createBuildConfig({
        name,
        external: ['@micro-core/core', '@micro-core/shared'],
        ...customConfig
    }),

    /**
     * 适配器包配置
     */
    adapter: (name: string, customConfig?: Partial<BuildConfigOptions>) => createBuildConfig({
        name,
        external: ['@micro-core/core', '@micro-core/shared'],
        ...customConfig
    }),

    /**
     * 工具包配置
     */
    utils: (name: string, customConfig?: Partial<BuildConfigOptions>) => createBuildConfig({
        name,
        external: ['@micro-core/shared'],
        ...customConfig
    })
};

/**
 * 默认导出
 */
export default {
    createBuildConfig,
    createDevConfig,
    createProdConfig,
    presets
};