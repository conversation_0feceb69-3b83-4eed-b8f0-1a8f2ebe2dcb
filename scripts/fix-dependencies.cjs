#!/usr/bin/env node

const { readFileSync, writeFileSync, readdirSync, statSync } = require('fs');
const path = require('path');

console.log('🔧 修复workspace依赖问题...');

// 递归获取所有package.json文件
function findPackageFiles(dir, files = []) {
    const items = readdirSync(dir);

    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = statSync(fullPath);

        if (stat.isDirectory()) {
            if (item !== 'node_modules' && item !== 'dist' && item !== '_backup') {
                findPackageFiles(fullPath, files);
            }
        } else if (item === 'package.json' && dir.includes('packages/')) {
            files.push(fullPath);
        }
    }

    return files;
}

// 递归获取所有源文件
function findSourceFiles(dir, files = []) {
    const items = readdirSync(dir);

    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = statSync(fullPath);

        if (stat.isDirectory()) {
            if (item !== 'node_modules' && item !== 'dist' && item !== '_backup') {
                findSourceFiles(fullPath, files);
            }
        } else if (/\.(ts|tsx|js|jsx)$/.test(item) && fullPath.includes('packages/') && fullPath.includes('/src/')) {
            files.push(fullPath);
        }
    }

    return files;
}

const packageFiles = findPackageFiles('./packages');

// 需要修复的依赖映射
const dependencyMapping = {
    '@micro-core/shared-types': '@micro-core/shared',
    '@micro-core/shared-constants': '@micro-core/shared',
    '@micro-core/shared-utils': '@micro-core/shared',
    '@micro-core/shared-helpers': '@micro-core/shared'
};

let fixedCount = 0;

packageFiles.forEach(file => {
    try {
        const content = readFileSync(file, 'utf8');
        const pkg = JSON.parse(content);
        let modified = false;

        // 修复dependencies
        if (pkg.dependencies) {
            Object.keys(pkg.dependencies).forEach(dep => {
                if (dependencyMapping[dep]) {
                    console.log(`🔄 修复依赖: ${pkg.name} -> ${dep} => ${dependencyMapping[dep]}`);
                    pkg.dependencies[dependencyMapping[dep]] = pkg.dependencies[dep];
                    delete pkg.dependencies[dep];
                    modified = true;
                }
            });
        }

        // 修复devDependencies
        if (pkg.devDependencies) {
            Object.keys(pkg.devDependencies).forEach(dep => {
                if (dependencyMapping[dep]) {
                    console.log(`🔄 修复开发依赖: ${pkg.name} -> ${dep} => ${dependencyMapping[dep]}`);
                    pkg.devDependencies[dependencyMapping[dep]] = pkg.devDependencies[dep];
                    delete pkg.devDependencies[dep];
                    modified = true;
                }
            });
        }

        // 修复peerDependencies
        if (pkg.peerDependencies) {
            Object.keys(pkg.peerDependencies).forEach(dep => {
                if (dependencyMapping[dep]) {
                    console.log(`🔄 修复peer依赖: ${pkg.name} -> ${dep} => ${dependencyMapping[dep]}`);
                    pkg.peerDependencies[dependencyMapping[dep]] = pkg.peerDependencies[dep];
                    delete pkg.peerDependencies[dep];
                    modified = true;
                }
            });
        }

        if (modified) {
            writeFileSync(file, JSON.stringify(pkg, null, 2) + '\n');
            fixedCount++;
        }
    } catch (error) {
        console.error(`❌ 处理文件失败: ${file}`, error.message);
    }
});

console.log(`✅ 依赖修复完成，共修复 ${fixedCount} 个包`);

// 同时修复源代码中的import语句
console.log('🔧 修复源代码中的import语句...');

const sourceFiles = findSourceFiles('./packages');
let importFixedCount = 0;

sourceFiles.forEach(file => {
    try {
        let content = readFileSync(file, 'utf8');
        let modified = false;

        Object.keys(dependencyMapping).forEach(oldDep => {
            const newDep = dependencyMapping[oldDep];
            const oldImportRegex = new RegExp(`from ['"]${oldDep}['"]`, 'g');

            if (oldImportRegex.test(content)) {
                content = content.replace(oldImportRegex, `from '${newDep}'`);
                modified = true;
            }
        });

        if (modified) {
            writeFileSync(file, content);
            importFixedCount++;
            console.log(`🔄 修复import: ${file}`);
        }
    } catch (error) {
        console.error(`❌ 处理源文件失败: ${file}`, error.message);
    }
});

console.log(`✅ import语句修复完成，共修复 ${importFixedCount} 个文件`);
console.log('🎉 所有依赖问题修复完成！');