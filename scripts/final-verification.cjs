#!/usr/bin/env node

const { readFileSync, existsSync, readdirSync, statSync } = require('fs');
const path = require('path');

console.log('🔍 执行最终项目验证...\n');

// 验证结果统计
const results = {
    packages: { total: 0, valid: 0, issues: [] },
    structure: { total: 0, valid: 0, issues: [] },
    dependencies: { total: 0, valid: 0, issues: [] },
    buildConfigs: { total: 0, valid: 0, issues: [] }
};

// 1. 验证核心包结构
console.log('📦 验证核心包结构...');
const corePackages = ['core', 'shared', 'plugins', 'adapters', 'builders', 'sidecar'];

corePackages.forEach(pkg => {
    results.packages.total++;
    const pkgPath = path.join('packages', pkg);
    
    if (!existsSync(pkgPath)) {
        results.packages.issues.push(`❌ 包目录不存在: ${pkg}`);
        return;
    }
    
    // 检查必要文件
    const requiredFiles = ['package.json', 'src/index.ts', 'vite.config.ts'];
    const missingFiles = requiredFiles.filter(file => !existsSync(path.join(pkgPath, file)));
    
    if (missingFiles.length > 0) {
        results.packages.issues.push(`❌ ${pkg} 缺少文件: ${missingFiles.join(', ')}`);
    } else {
        results.packages.valid++;
        console.log(`✅ ${pkg}: 结构完整`);
    }
});

// 2. 验证包依赖关系
console.log('\n🔗 验证包依赖关系...');
corePackages.forEach(pkg => {
    results.dependencies.total++;
    const packageJsonPath = path.join('packages', pkg, 'package.json');
    
    if (!existsSync(packageJsonPath)) {
        results.dependencies.issues.push(`❌ ${pkg}: package.json不存在`);
        return;
    }
    
    try {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        
        // 检查是否还有旧的依赖引用
        const allDeps = {
            ...packageJson.dependencies,
            ...packageJson.devDependencies,
            ...packageJson.peerDependencies
        };
        
        const oldDeps = Object.keys(allDeps).filter(dep => 
            dep.includes('shared-types') || 
            dep.includes('shared-constants') || 
            dep.includes('shared-utils') || 
            dep.includes('shared-helpers')
        );
        
        if (oldDeps.length > 0) {
            results.dependencies.issues.push(`❌ ${pkg}: 仍有旧依赖 ${oldDeps.join(', ')}`);
        } else {
            results.dependencies.valid++;
            console.log(`✅ ${pkg}: 依赖关系正确`);
        }
    } catch (error) {
        results.dependencies.issues.push(`❌ ${pkg}: package.json解析失败`);
    }
});

// 3. 验证构建配置
console.log('\n⚙️ 验证构建配置...');
corePackages.forEach(pkg => {
    results.buildConfigs.total++;
    const viteConfigPath = path.join('packages', pkg, 'vite.config.ts');
    const tsupConfigPath = path.join('packages', pkg, 'tsup.config.ts');
    
    if (existsSync(tsupConfigPath)) {
        results.buildConfigs.issues.push(`❌ ${pkg}: 仍存在tsup配置文件`);
    } else if (!existsSync(viteConfigPath)) {
        results.buildConfigs.issues.push(`❌ ${pkg}: 缺少vite配置文件`);
    } else {
        results.buildConfigs.valid++;
        console.log(`✅ ${pkg}: 构建配置正确`);
    }
});

// 4. 验证目录结构深度
console.log('\n📁 验证目录结构深度...');
function checkDirectoryDepth(dir, maxDepth = 4, currentDepth = 0) {
    if (currentDepth > maxDepth) {
        return [`目录深度超过${maxDepth}层: ${dir}`];
    }
    
    const issues = [];
    
    if (existsSync(dir) && statSync(dir).isDirectory()) {
        const items = readdirSync(dir);
        
        for (const item of items) {
            if (item === 'node_modules' || item === 'dist' || item === '_backup') continue;
            
            const fullPath = path.join(dir, item);
            if (statSync(fullPath).isDirectory()) {
                issues.push(...checkDirectoryDepth(fullPath, maxDepth, currentDepth + 1));
            }
        }
    }
    
    return issues;
}

corePackages.forEach(pkg => {
    results.structure.total++;
    const pkgPath = path.join('packages', pkg);
    const depthIssues = checkDirectoryDepth(pkgPath);
    
    if (depthIssues.length > 0) {
        results.structure.issues.push(`❌ ${pkg}: ${depthIssues.join(', ')}`);
    } else {
        results.structure.valid++;
        console.log(`✅ ${pkg}: 目录深度合理`);
    }
});

// 5. 生成验证报告
console.log('\n📊 验证结果汇总:');
console.log('==========================================');

const categories = [
    { name: '包结构', key: 'packages' },
    { name: '依赖关系', key: 'dependencies' },
    { name: '构建配置', key: 'buildConfigs' },
    { name: '目录结构', key: 'structure' }
];

let totalScore = 0;
let maxScore = 0;

categories.forEach(category => {
    const result = results[category.key];
    const score = result.valid;
    const total = result.total;
    const percentage = total > 0 ? Math.round((score / total) * 100) : 0;
    
    console.log(`${category.name}: ${score}/${total} (${percentage}%)`);
    
    if (result.issues.length > 0) {
        result.issues.forEach(issue => console.log(`  ${issue}`));
    }
    
    totalScore += score;
    maxScore += total;
});

const overallScore = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;

console.log('==========================================');
console.log(`总体评分: ${totalScore}/${maxScore} (${overallScore}%)`);

if (overallScore >= 90) {
    console.log('🎉 项目优化质量: 优秀');
} else if (overallScore >= 80) {
    console.log('👍 项目优化质量: 良好');
} else if (overallScore >= 70) {
    console.log('⚠️ 项目优化质量: 一般，需要改进');
} else {
    console.log('❌ 项目优化质量: 较差，需要重新优化');
}

// 6. 检查优化成果
console.log('\n🎯 优化成果检查:');

// 检查备份目录
const backupDirs = ['_backup/tsup-configs', '_backup/redundant-files', '_backup/old-structure'];
backupDirs.forEach(dir => {
    if (existsSync(dir)) {
        console.log(`✅ 备份完整: ${dir}`);
    } else {
        console.log(`❌ 备份缺失: ${dir}`);
    }
});

// 检查根目录配置
const rootConfigs = ['turbo.json', 'package.json', 'pnpm-workspace.yaml'];
rootConfigs.forEach(config => {
    if (existsSync(config)) {
        console.log(`✅ 根配置存在: ${config}`);
    } else {
        console.log(`❌ 根配置缺失: ${config}`);
    }
});

console.log('\n✨ 验证完成！');

// 返回退出码
process.exit(overallScore >= 80 ? 0 : 1);