#!/usr/bin/env node

/**
 * 重构 packages/shared 包结构
 * 按照项目优化建议文档执行深度重构
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🏗️ 开始重构 packages/shared 包结构...\n');

const SHARED_PATH = 'packages/shared';
const NEW_STRUCTURE = {
    'src/constants': [],
    'src/types': [],
    'src/utils': [],
    'src/errors': [],
    '__tests__/unit': [],
    '__tests__/integration': []
};

/**
 * 清理并重建 shared 包结构
 */
function refactorSharedStructure() {
    console.log('📁 重构 shared 包目录结构');

    // 1. 创建新的标准化结构
    Object.keys(NEW_STRUCTURE).forEach(dir => {
        const fullPath = path.join(SHARED_PATH, dir);
        if (!fs.existsSync(fullPath)) {
            fs.mkdirSync(fullPath, { recursive: true });
            console.log(`✅ 创建目录: ${fullPath}`);
        }
    });

    // 2. 整合常量定义
    consolidateConstants();

    // 3. 整合类型定义
    consolidateTypes();

    // 4. 整合工具函数
    consolidateUtils();

    // 5. 创建错误处理模块
    createErrorModule();

    // 6. 更新主入口文件
    updateMainIndex();

    // 7. 清理冗余目录
    cleanupRedundantDirs();

    console.log('✅ shared 包重构完成\n');
}

/**
 * 整合常量定义
 */
function consolidateConstants() {
    console.log('🔧 整合常量定义');

    const constantsContent = `/**
 * Micro-Core 核心常量定义
 * 统一管理所有常量，避免重复定义
 */

// 应用状态常量
export const APP_STATUS = {
    NOT_LOADED: 'NOT_LOADED',
    LOADING_SOURCE_CODE: 'LOADING_SOURCE_CODE',
    NOT_BOOTSTRAPPED: 'NOT_BOOTSTRAPPED',
    BOOTSTRAPPING: 'BOOTSTRAPPING',
    NOT_MOUNTED: 'NOT_MOUNTED',
    MOUNTING: 'MOUNTING',
    MOUNTED: 'MOUNTED',
    UPDATING: 'UPDATING',
    UNMOUNTING: 'UNMOUNTING',
    UNLOADING: 'UNLOADING',
    LOAD_ERROR: 'LOAD_ERROR',
    SKIP_BECAUSE_BROKEN: 'SKIP_BECAUSE_BROKEN'
} as const;

// 沙箱类型常量
export const SANDBOX_TYPES = {
    PROXY: 'proxy',
    DEFINE_PROPERTY: 'define-property',
    WEB_COMPONENT: 'web-component',
    IFRAME: 'iframe',
    NAMESPACE: 'namespace',
    FEDERATION: 'federation'
} as const;

// 生命周期钩子常量
export const LIFECYCLE_HOOKS = {
    BEFORE_LOAD: 'beforeLoad',
    AFTER_LOAD: 'afterLoad',
    BEFORE_BOOTSTRAP: 'beforeBootstrap',
    AFTER_BOOTSTRAP: 'afterBootstrap',
    BEFORE_MOUNT: 'beforeMount',
    AFTER_MOUNT: 'afterMount',
    BEFORE_UPDATE: 'beforeUpdate',
    AFTER_UPDATE: 'afterUpdate',
    BEFORE_UNMOUNT: 'beforeUnmount',
    AFTER_UNMOUNT: 'afterUnmount',
    BEFORE_UNLOAD: 'beforeUnload',
    AFTER_UNLOAD: 'afterUnload'
} as const;

// 事件类型常量
export const EVENT_TYPES = {
    APP_REGISTERED: 'app:registered',
    APP_LOADED: 'app:loaded',
    APP_BOOTSTRAPPED: 'app:bootstrapped',
    APP_MOUNTED: 'app:mounted',
    APP_UPDATED: 'app:updated',
    APP_UNMOUNTED: 'app:unmounted',
    APP_UNLOADED: 'app:unloaded',
    APP_ERROR: 'app:error',
    ROUTE_CHANGED: 'route:changed',
    PLUGIN_INSTALLED: 'plugin:installed',
    PLUGIN_UNINSTALLED: 'plugin:uninstalled'
} as const;

// 错误码常量
export const ERROR_CODES = {
    // 系统错误 1000-1999
    SYSTEM_INIT_FAILED: 1001,
    SYSTEM_DESTROY_FAILED: 1002,
    
    // 应用错误 2000-2999
    APP_LOAD_FAILED: 2001,
    APP_BOOTSTRAP_FAILED: 2002,
    APP_MOUNT_FAILED: 2003,
    APP_UNMOUNT_FAILED: 2004,
    APP_UPDATE_FAILED: 2005,
    APP_UNLOAD_FAILED: 2006,
    
    // 沙箱错误 3000-3999
    SANDBOX_CREATE_FAILED: 3001,
    SANDBOX_ACTIVATE_FAILED: 3002,
    SANDBOX_DEACTIVATE_FAILED: 3003,
    SANDBOX_DESTROY_FAILED: 3004,
    
    // 插件错误 4000-4999
    PLUGIN_INSTALL_FAILED: 4001,
    PLUGIN_UNINSTALL_FAILED: 4002,
    PLUGIN_CONFIG_INVALID: 4003,
    
    // 路由错误 5000-5999
    ROUTE_MATCH_FAILED: 5001,
    ROUTE_NAVIGATE_FAILED: 5002,
    
    // 通信错误 6000-6999
    COMMUNICATION_SEND_FAILED: 6001,
    COMMUNICATION_RECEIVE_FAILED: 6002
} as const;

// 资源类型常量
export const RESOURCE_TYPES = {
    SCRIPT: 'script',
    STYLE: 'style',
    HTML: 'html',
    JSON: 'json',
    IMAGE: 'image',
    FONT: 'font',
    OTHER: 'other'
} as const;

// 性能指标常量
export const PERFORMANCE_METRICS = {
    APP_LOAD_TIME: 'app_load_time',
    APP_BOOTSTRAP_TIME: 'app_bootstrap_time',
    APP_MOUNT_TIME: 'app_mount_time',
    MEMORY_USAGE: 'memory_usage',
    BUNDLE_SIZE: 'bundle_size'
} as const;

// 默认配置常量
export const DEFAULT_CONFIG = {
    SANDBOX_TYPE: SANDBOX_TYPES.PROXY,
    TIMEOUT: 30000,
    RETRY_COUNT: 3,
    CACHE_ENABLED: true,
    DEBUG: false
} as const;
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/constants/index.ts'), constantsContent);
    console.log('✅ 创建统一常量文件');
}

/**
 * 整合类型定义
 */
function consolidateTypes() {
    console.log('🔧 整合类型定义');

    const typesContent = `/**
 * Micro-Core 核心类型定义
 * 统一管理所有类型，确保类型一致性
 */

import type { APP_STATUS, SANDBOX_TYPES, LIFECYCLE_HOOKS, EVENT_TYPES, ERROR_CODES } from '../constants';

// 基础类型
export type AppStatus = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type LifecycleHook = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

// 应用配置类型
export interface AppConfig {
    name: string;
    entry: string | AppEntry;
    container: string | HTMLElement;
    activeWhen: string | ((location: Location) => boolean);
    props?: Record<string, any>;
    sandbox?: SandboxConfig;
    loader?: LoaderConfig;
    customProps?: Record<string, any>;
}

export interface AppEntry {
    scripts?: string[];
    styles?: string[];
    html?: string;
}

// 沙箱配置类型
export interface SandboxConfig {
    type: SandboxType;
    strict?: boolean;
    excludeAssetFilter?: (url: string) => boolean;
    plugins?: SandboxPlugin[];
}

export interface SandboxPlugin {
    name: string;
    apply: (sandbox: SandboxInstance) => void;
}

// 应用实例类型
export interface AppInstance {
    name: string;
    status: AppStatus;
    config: AppConfig;
    sandbox?: SandboxInstance;
    loadPromise?: Promise<void>;
    bootstrapPromise?: Promise<void>;
    mountPromise?: Promise<void>;
    unmountPromise?: Promise<void>;
    unloadPromise?: Promise<void>;
}

// 沙箱实例类型
export interface SandboxInstance {
    name: string;
    type: SandboxType;
    active: boolean;
    activate(): Promise<void>;
    deactivate(): Promise<void>;
    execScript(code: string, filename?: string): Promise<any>;
    destroy(): Promise<void>;
}

// 插件类型
export interface Plugin {
    name: string;
    version: string;
    description?: string;
    dependencies?: string[];
    install(kernel: MicroCoreKernel, options?: any): Promise<void> | void;
    uninstall?(kernel: MicroCoreKernel): Promise<void> | void;
}

// 内核类型
export interface MicroCoreKernel {
    registerApp(config: AppConfig): Promise<void>;
    unregisterApp(name: string): Promise<void>;
    loadApp(name: string): Promise<void>;
    unloadApp(name: string): Promise<void>;
    mountApp(name: string, props?: Record<string, any>): Promise<void>;
    unmountApp(name: string): Promise<void>;
    getApp(name: string): AppInstance | undefined;
    getAllApps(): AppInstance[];
    use(plugin: Plugin, options?: any): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
}

// 生命周期函数类型
export type LifecycleFn<T = any> = (app: AppInstance, ...args: any[]) => Promise<T> | T;

// 事件类型
export interface MicroCoreEvent {
    type: EventType;
    app?: AppInstance;
    data?: any;
    timestamp: number;
}

// 错误类型
export interface MicroCoreError extends Error {
    code: ErrorCode;
    app?: string;
    data?: Record<string, any>;
    cause?: Error;
}

// 加载器配置类型
export interface LoaderConfig {
    timeout?: number;
    retries?: number;
    cache?: boolean;
    credentials?: RequestCredentials;
    headers?: Record<string, string>;
}

// 路由类型
export interface RouteConfig {
    path: string;
    app: string;
    exact?: boolean;
    props?: Record<string, any>;
}

// 通信类型
export interface CommunicationMessage {
    type: string;
    from: string;
    to: string;
    data: any;
    timestamp: number;
}

// 性能监控类型
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: number;
    app?: string;
}

// 工具类型
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Awaitable<T> = T | Promise<T>;

export type Fn<T = void> = () => T;

export type AnyFn = (...args: any[]) => any;
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/types/index.ts'), typesContent);
    console.log('✅ 创建统一类型文件');
}

/**
 * 整合工具函数
 */
function consolidateUtils() {
    console.log('🔧 整合工具函数');

    const utilsContent = `/**
 * Micro-Core 核心工具函数
 * 提供通用的工具函数，避免重复实现
 */

import type { AppConfig, MicroCoreError, PerformanceMetric } from '../types';
import { ERROR_CODES } from '../constants';

// 类型检查工具
export const isString = (value: unknown): value is string => typeof value === 'string';
export const isNumber = (value: unknown): value is number => typeof value === 'number';
export const isBoolean = (value: unknown): value is boolean => typeof value === 'boolean';
export const isFunction = (value: unknown): value is Function => typeof value === 'function';
export const isObject = (value: unknown): value is object => value !== null && typeof value === 'object';
export const isArray = (value: unknown): value is any[] => Array.isArray(value);
export const isPromise = (value: unknown): value is Promise<any> => 
    isObject(value) && isFunction((value as any).then);

// 字符串工具
export function kebabCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

export function camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

export function capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

// 对象工具
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as any;
    if (obj instanceof Array) return obj.map(item => deepClone(item)) as any;
    if (typeof obj === 'object') {
        const cloned = {} as any;
        Object.keys(obj).forEach(key => {
            cloned[key] = deepClone((obj as any)[key]);
        });
        return cloned;
    }
    return obj;
}

export function deepMerge<T extends object>(target: T, ...sources: Partial<T>[]): T {
    if (!sources.length) return target;
    const source = sources.shift();
    
    if (isObject(target) && isObject(source)) {
        for (const key in source) {
            if (isObject(source[key])) {
                if (!target[key]) Object.assign(target, { [key]: {} });
                deepMerge(target[key] as any, source[key] as any);
            } else {
                Object.assign(target, { [key]: source[key] });
            }
        }
    }
    
    return deepMerge(target, ...sources);
}

// 异步工具
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error(\`Timeout after \${ms}ms\`)), ms)
        )
    ]);
}

export function retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
): Promise<T> {
    return fn().catch(async (error) => {
        if (maxRetries <= 0) throw error;
        await sleep(delay);
        return retry(fn, maxRetries - 1, delay);
    });
}

// DOM 工具
export function createElement(tag: string, attrs?: Record<string, string>): HTMLElement {
    const element = document.createElement(tag);
    if (attrs) {
        Object.entries(attrs).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
    }
    return element;
}

export function loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = createElement('script', { src, type: 'text/javascript' });
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(\`Failed to load script: \${src}\`));
        document.head.appendChild(script);
    });
}

export function loadStyle(href: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const link = createElement('link', { href, rel: 'stylesheet' });
        link.onload = () => resolve();
        link.onerror = () => reject(new Error(\`Failed to load style: \${href}\`));
        document.head.appendChild(link);
    });
}

// URL 工具
export function isAbsoluteUrl(url: string): boolean {
    return /^https?:\\/\\//.test(url);
}

export function resolveUrl(base: string, relative: string): string {
    if (isAbsoluteUrl(relative)) return relative;
    return new URL(relative, base).href;
}

export function parseUrl(url: string): URL {
    return new URL(url);
}

// 性能工具
export function measurePerformance<T>(
    name: string,
    fn: () => T | Promise<T>
): T extends Promise<any> ? Promise<T> : T {
    const start = performance.now();
    const result = fn();
    
    if (isPromise(result)) {
        return result.then(value => {
            const end = performance.now();
            console.log(\`[\${name}] 执行时间: \${(end - start).toFixed(2)}ms\`);
            return value;
        }) as any;
    } else {
        const end = performance.now();
        console.log(\`[\${name}] 执行时间: \${(end - start).toFixed(2)}ms\`);
        return result as any;
    }
}

// 验证工具
export function validateAppConfig(config: AppConfig): void {
    if (!config.name) {
        throw new Error('应用名称不能为空');
    }
    
    if (!config.entry) {
        throw new Error('应用入口不能为空');
    }
    
    if (!config.container) {
        throw new Error('应用容器不能为空');
    }
    
    if (!config.activeWhen) {
        throw new Error('应用激活条件不能为空');
    }
}

// 环境检测工具
export function isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof document !== 'undefined';
}

export function isNode(): boolean {
    return typeof process !== 'undefined' && process.versions && process.versions.node;
}

export function getEnv(): 'browser' | 'node' | 'unknown' {
    if (isBrowser()) return 'browser';
    if (isNode()) return 'node';
    return 'unknown';
}

// 日志工具
export const logger = {
    debug: (message: string, ...args: any[]) => {
        if (process.env.NODE_ENV === 'development') {
            console.debug(\`[Micro-Core Debug] \${message}\`, ...args);
        }
    },
    
    info: (message: string, ...args: any[]) => {
        console.info(\`[Micro-Core Info] \${message}\`, ...args);
    },
    
    warn: (message: string, ...args: any[]) => {
        console.warn(\`[Micro-Core Warn] \${message}\`, ...args);
    },
    
    error: (message: string, ...args: any[]) => {
        console.error(\`[Micro-Core Error] \${message}\`, ...args);
    }
};
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/utils/index.ts'), utilsContent);
    console.log('✅ 创建统一工具函数文件');
}

/**
 * 创建错误处理模块
 */
function createErrorModule() {
    console.log('🔧 创建错误处理模块');

    const errorsContent = `/**
 * Micro-Core 错误处理模块
 * 提供统一的错误处理机制
 */

import type { MicroCoreError as IMicroCoreError, ErrorCode } from '../types';
import { ERROR_CODES } from '../constants';

/**
 * Micro-Core 自定义错误类
 */
export class MicroCoreError extends Error implements IMicroCoreError {
    public readonly code: ErrorCode;
    public readonly app?: string;
    public readonly data?: Record<string, any>;
    public readonly cause?: Error;

    constructor(
        code: ErrorCode,
        message: string,
        app?: string,
        data?: Record<string, any>,
        cause?: Error
    ) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code;
        this.app = app;
        this.data = data;
        this.cause = cause;

        // 保持堆栈跟踪
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    /**
     * 从错误码创建错误实例
     */
    static fromCode(
        code: ErrorCode,
        app?: string,
        data?: Record<string, any>,
        cause?: Error
    ): MicroCoreError {
        const message = ERROR_MESSAGES[code] || '未知错误';
        return new MicroCoreError(code, message, app, data, cause);
    }

    /**
     * 转换为 JSON 格式
     */
    toJSON(): object {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            app: this.app,
            data: this.data,
            stack: this.stack,
            cause: this.cause?.message
        };
    }
}

/**
 * 错误消息映射
 */
const ERROR_MESSAGES: Record<ErrorCode, string> = {
    // 系统错误
    [ERROR_CODES.SYSTEM_INIT_FAILED]: '系统初始化失败',
    [ERROR_CODES.SYSTEM_DESTROY_FAILED]: '系统销毁失败',
    
    // 应用错误
    [ERROR_CODES.APP_LOAD_FAILED]: '应用加载失败',
    [ERROR_CODES.APP_BOOTSTRAP_FAILED]: '应用启动失败',
    [ERROR_CODES.APP_MOUNT_FAILED]: '应用挂载失败',
    [ERROR_CODES.APP_UNMOUNT_FAILED]: '应用卸载失败',
    [ERROR_CODES.APP_UPDATE_FAILED]: '应用更新失败',
    [ERROR_CODES.APP_UNLOAD_FAILED]: '应用销毁失败',
    
    // 沙箱错误
    [ERROR_CODES.SANDBOX_CREATE_FAILED]: '沙箱创建失败',
    [ERROR_CODES.SANDBOX_ACTIVATE_FAILED]: '沙箱激活失败',
    [ERROR_CODES.SANDBOX_DEACTIVATE_FAILED]: '沙箱停用失败',
    [ERROR_CODES.SANDBOX_DESTROY_FAILED]: '沙箱销毁失败',
    
    // 插件错误
    [ERROR_CODES.PLUGIN_INSTALL_FAILED]: '插件安装失败',
    [ERROR_CODES.PLUGIN_UNINSTALL_FAILED]: '插件卸载失败',
    [ERROR_CODES.PLUGIN_CONFIG_INVALID]: '插件配置无效',
    
    // 路由错误
    [ERROR_CODES.ROUTE_MATCH_FAILED]: '路由匹配失败',
    [ERROR_CODES.ROUTE_NAVIGATE_FAILED]: '路由导航失败',
    
    // 通信错误
    [ERROR_CODES.COMMUNICATION_SEND_FAILED]: '消息发送失败',
    [ERROR_CODES.COMMUNICATION_RECEIVE_FAILED]: '消息接收失败'
};

/**
 * 全局错误处理器
 */
export class ErrorHandler {
    private static handlers = new Map<ErrorCode, (error: MicroCoreError) => void>();

    /**
     * 注册错误处理器
     */
    static register(code: ErrorCode, handler: (error: MicroCoreError) => void): void {
        this.handlers.set(code, handler);
    }

    /**
     * 处理错误
     */
    static handle(error: unknown): void {
        if (error instanceof MicroCoreError) {
            const handler = this.handlers.get(error.code);
            if (handler) {
                handler(error);
            } else {
                this.defaultHandler(error);
            }
        } else if (error instanceof Error) {
            this.defaultHandler(new MicroCoreError(
                ERROR_CODES.SYSTEM_INIT_FAILED,
                error.message,
                undefined,
                undefined,
                error
            ));
        } else {
            this.defaultHandler(new MicroCoreError(
                ERROR_CODES.SYSTEM_INIT_FAILED,
                String(error)
            ));
        }
    }

    /**
     * 默认错误处理器
     */
    private static defaultHandler(error: MicroCoreError): void {
        console.error(\`[Micro-Core Error \${error.code}] \${error.message}\`, {
            app: error.app,
            data: error.data,
            cause: error.cause,
            stack: error.stack
        });
    }
}

/**
 * 错误工厂函数
 */
export const createError = {
    system: (message: string, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.SYSTEM_INIT_FAILED, message, undefined, undefined, cause),
    
    app: (message: string, app: string, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.APP_LOAD_FAILED, message, app, undefined, cause),
    
    sandbox: (message: string, app: string, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.SANDBOX_CREATE_FAILED, message, app, undefined, cause),
    
    plugin: (message: string, data?: Record<string, any>, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.PLUGIN_INSTALL_FAILED, message, undefined, data, cause),
    
    route: (message: string, data?: Record<string, any>, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.ROUTE_MATCH_FAILED, message, undefined, data, cause),
    
    communication: (message: string, data?: Record<string, any>, cause?: Error) => 
        new MicroCoreError(ERROR_CODES.COMMUNICATION_SEND_FAILED, message, undefined, data, cause)
};
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/errors/index.ts'), errorsContent);
    console.log('✅ 创建错误处理模块');
}

/**
 * 更新主入口文件
 */
function updateMainIndex() {
    console.log('🔧 更新主入口文件');

    const indexContent = `/**
 * @micro-core/shared
 * Micro-Core 共享工具包
 * 
 * 提供统一的常量、类型、工具函数和错误处理
 * 
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 * @license MIT
 */

// 导出常量
export * from './constants';

// 导出类型
export * from './types';

// 导出工具函数
export * from './utils';

// 导出错误处理
export * from './errors';

// 版本信息
export const VERSION = '0.1.0';

// 默认导出
export default {
    VERSION
};
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/index.ts'), indexContent);
    console.log('✅ 更新主入口文件');
}

/**
 * 清理冗余目录
 */
function cleanupRedundantDirs() {
    console.log('🧹 清理冗余目录');

    const redundantDirs = [
        'constants',
        'types',
        'utils',
        'dev-config',
        'test-utils',
        'docs'
    ];

    redundantDirs.forEach(dir => {
        const dirPath = path.join(SHARED_PATH, dir);
        if (fs.existsSync(dirPath)) {
            try {
                execSync(`rm -rf "${dirPath}"`, { stdio: 'inherit' });
                console.log(`🗑️ 删除冗余目录: ${dir}`);
            } catch (error) {
                console.warn(`⚠️ 删除目录失败: ${dir}`);
            }
        }
    });

    console.log('✅ 清理冗余目录完成');
}

// 执行重构
refactorSharedStructure();

console.log('🎉 packages/shared 重构完成！');
console.log('\n📋 重构总结:');
console.log('✅ 统一常量定义到 src/constants/index.ts');
console.log('✅ 统一类型定义到 src/types/index.ts');
console.log('✅ 统一工具函数到 src/utils/index.ts');
console.log('✅ 创建错误处理模块到 src/errors/index.ts');
console.log('✅ 更新主入口文件 src/index.ts');
console.log('✅ 清理冗余目录结构');
console.log('✅ 遵循单一职责原则，避免目录深度过深');
