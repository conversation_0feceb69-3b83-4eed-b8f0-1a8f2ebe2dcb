#!/usr/bin/env node

/**
 * @fileoverview 简化清理脚本
 * @description 不依赖外部包的简单清理工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import fs from 'fs';
import path from 'path';

/**
 * 备份目录
 */
const BACKUP_DIR = '_backup';

/**
 * 需要清理的文件扩展名
 */
const CLEANUP_EXTENSIONS = ['.bak', '.tmp', '.temp', '.swp', '.swo'];

/**
 * 需要清理的文件名模式
 */
const CLEANUP_NAMES = ['.DS_Store', 'Thumbs.db', 'tsup.config.ts', 'tsup.config.js'];

/**
 * 需要保留的目录
 */
const PRESERVE_DIRS = ['node_modules', '.git', 'dist', 'coverage', '_backup'];

/**
 * 确保目录存在
 * @param {string} dirPath 目录路径
 */
function ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

/**
 * 检查路径是否应该被保留
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否应该保留
 */
function shouldPreserve(filePath) {
    const parts = filePath.split(path.sep);
    return PRESERVE_DIRS.some(dir => parts.includes(dir));
}

/**
 * 递归扫描目录
 * @param {string} dir 目录路径
 * @param {Array} results 结果数组
 */
function scanDirectory(dir, results = []) {
    if (shouldPreserve(dir)) {
        return results;
    }

    try {
        const items = fs.readdirSync(dir);

        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stats = fs.statSync(fullPath);

            if (stats.isDirectory()) {
                scanDirectory(fullPath, results);
            } else if (stats.isFile()) {
                results.push(fullPath);
            }
        }
    } catch (error) {
        // 忽略无法访问的目录
    }

    return results;
}

/**
 * 检查文件是否需要清理
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否需要清理
 */
function shouldCleanup(filePath) {
    const fileName = path.basename(filePath);
    const ext = path.extname(filePath);

    // 检查扩展名
    if (CLEANUP_EXTENSIONS.includes(ext)) {
        return true;
    }

    // 检查文件名
    if (CLEANUP_NAMES.includes(fileName)) {
        return true;
    }

    // 检查特殊模式
    if (fileName.includes('-copy') || fileName.includes('-backup') || fileName.includes('duplicate-')) {
        return true;
    }

    return false;
}

/**
 * 备份文件
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否成功
 */
function backupFile(filePath) {
    try {
        const relativePath = path.relative(process.cwd(), filePath);
        const backupPath = path.join(process.cwd(), BACKUP_DIR, relativePath);
        const backupDir = path.dirname(backupPath);

        ensureDir(backupDir);
        fs.renameSync(filePath, backupPath);

        console.log(`📦 备份: ${relativePath}`);
        return true;
    } catch (error) {
        console.error(`❌ 备份失败 ${filePath}:`, error.message);
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🧹 开始简单清理...\n');

    // 创建备份目录
    ensureDir(path.join(process.cwd(), BACKUP_DIR));

    let totalBackedUp = 0;

    try {
        // 扫描所有文件
        console.log('📋 扫描文件...');
        const allFiles = scanDirectory(process.cwd());

        console.log(`发现 ${allFiles.length} 个文件`);

        // 清理文件
        for (const filePath of allFiles) {
            if (shouldCleanup(filePath)) {
                if (backupFile(filePath)) {
                    totalBackedUp++;
                }
            }
        }

        // 生成报告
        console.log('\n📊 清理报告:');
        console.log(`- 备份文件数: ${totalBackedUp}`);
        console.log(`- 备份位置: ${BACKUP_DIR}/`);

        if (totalBackedUp > 0) {
            console.log('\n🎉 清理完成！');
            console.log('\n💡 建议操作:');
            console.log('1. 检查备份文件确认无误');
            console.log('2. 运行测试确保功能正常');
            console.log('3. 如果一切正常，可以删除备份目录');
        } else {
            console.log('\n✨ 项目已经很干净，无需清理！');
        }

    } catch (error) {
        console.error('❌ 清理过程中出错:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main().catch(error => {
    console.error('❌ 执行失败:', error);
    process.exit(1);
});