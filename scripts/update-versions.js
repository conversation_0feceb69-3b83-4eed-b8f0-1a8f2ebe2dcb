#!/usr/bin/env node

/**
 * @fileoverview 版本管理脚本
 * @description 统一更新所有包和文件的版本号
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import fs from 'fs';
import glob from 'glob';
import path from 'path';

/**
 * 获取命令行参数
 */
const args = process.argv.slice(2);
const newVersion = args[0];

if (!newVersion) {
    console.error('❌ 请提供新版本号');
    console.log('用法: node scripts/update-versions.js <version>');
    console.log('示例: node scripts/update-versions.js 0.2.0');
    process.exit(1);
}

// 验证版本号格式
const versionRegex = /^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/;
if (!versionRegex.test(newVersion)) {
    console.error('❌ 版本号格式不正确，应为 x.y.z 或 x.y.z-tag');
    process.exit(1);
}

console.log(`🚀 开始更新版本号至: ${newVersion}`);

/**
 * 更新package.json文件
 * @param {string} filePath 文件路径
 */
function updatePackageJson(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf-8');
        const pkg = JSON.parse(content);

        const oldVersion = pkg.version;
        pkg.version = newVersion;

        // 更新workspace依赖版本
        if (pkg.dependencies) {
            Object.keys(pkg.dependencies).forEach(dep => {
                if (dep.startsWith('@micro-core/') && pkg.dependencies[dep] === 'workspace:*') {
                    // workspace依赖保持不变
                    return;
                }
            });
        }

        fs.writeFileSync(filePath, JSON.stringify(pkg, null, 2) + '\n');
        console.log(`✅ 更新 ${path.relative(process.cwd(), filePath)}: ${oldVersion} → ${newVersion}`);
    } catch (error) {
        console.error(`❌ 更新 ${filePath} 失败:`, error.message);
    }
}

/**
 * 更新TypeScript源文件中的版本注释
 * @param {string} filePath 文件路径
 */
function updateSourceFileVersion(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf-8');
        let updated = false;

        // 更新 @version 注释
        const versionCommentRegex = /@version\s+\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?/g;
        if (versionCommentRegex.test(content)) {
            content = content.replace(versionCommentRegex, `@version ${newVersion}`);
            updated = true;
        }

        // 更新 VERSION 常量
        const versionConstRegex = /export\s+const\s+VERSION\s*=\s*['"`]\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?['"`]/g;
        if (versionConstRegex.test(content)) {
            content = content.replace(versionConstRegex, `export const VERSION = '${newVersion}'`);
            updated = true;
        }

        // 更新其他版本字符串
        const versionStringRegex = /version:\s*['"`]\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?['"`]/g;
        if (versionStringRegex.test(content)) {
            content = content.replace(versionStringRegex, `version: '${newVersion}'`);
            updated = true;
        }

        if (updated) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ 更新源文件版本: ${path.relative(process.cwd(), filePath)}`);
        }
    } catch (error) {
        console.error(`❌ 更新源文件 ${filePath} 失败:`, error.message);
    }
}

/**
 * 更新README文件中的版本信息
 * @param {string} filePath 文件路径
 */
function updateReadmeVersion(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf-8');
        let updated = false;

        // 更新版本徽章
        const badgeRegex = /version-\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?-/g;
        if (badgeRegex.test(content)) {
            content = content.replace(badgeRegex, `version-${newVersion}-`);
            updated = true;
        }

        // 更新版本号引用
        const versionRefRegex = /v\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?/g;
        if (versionRefRegex.test(content)) {
            content = content.replace(versionRefRegex, `v${newVersion}`);
            updated = true;
        }

        if (updated) {
            fs.writeFileSync(filePath, content);
            console.log(`✅ 更新README版本: ${path.relative(process.cwd(), filePath)}`);
        }
    } catch (error) {
        console.error(`❌ 更新README ${filePath} 失败:`, error.message);
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        // 1. 更新根package.json
        console.log('\n📦 更新package.json文件...');
        const rootPackagePath = path.join(process.cwd(), 'package.json');
        if (fs.existsSync(rootPackagePath)) {
            updatePackageJson(rootPackagePath);
        }

        // 2. 更新所有子包的package.json
        const packagePaths = glob.sync('packages/*/package.json', { cwd: process.cwd() });
        packagePaths.forEach(packagePath => {
            updatePackageJson(path.join(process.cwd(), packagePath));
        });

        // 3. 更新TypeScript源文件
        console.log('\n📝 更新源文件版本注释...');
        const sourceFiles = glob.sync('packages/*/src/**/*.ts', { cwd: process.cwd() });
        sourceFiles.forEach(filePath => {
            updateSourceFileVersion(path.join(process.cwd(), filePath));
        });

        // 4. 更新README文件
        console.log('\n📖 更新README文件...');
        const readmeFiles = glob.sync('**/README.md', {
            cwd: process.cwd(),
            ignore: ['node_modules/**', 'dist/**', 'coverage/**']
        });
        readmeFiles.forEach(filePath => {
            updateReadmeVersion(path.join(process.cwd(), filePath));
        });

        // 5. 更新构建配置文件
        console.log('\n⚙️ 更新构建配置...');
        const buildConfigPath = path.join(process.cwd(), 'build.config.ts');
        if (fs.existsSync(buildConfigPath)) {
            updateSourceFileVersion(buildConfigPath);
        }

        console.log(`\n🎉 版本更新完成！所有文件已更新至版本 ${newVersion}`);

        // 6. 生成更新报告
        console.log('\n📊 更新报告:');
        console.log(`- package.json文件: ${packagePaths.length + 1}个`);
        console.log(`- TypeScript源文件: ${sourceFiles.length}个`);
        console.log(`- README文件: ${readmeFiles.length}个`);
        console.log(`- 构建配置文件: 1个`);

        console.log('\n💡 建议执行以下命令:');
        console.log('1. git add .');
        console.log(`2. git commit -m "chore: bump version to ${newVersion}"`);
        console.log(`3. git tag v${newVersion}`);
        console.log('4. pnpm run build');

    } catch (error) {
        console.error('❌ 版本更新失败:', error.message);
        process.exit(1);
    }
}

// 依赖检查已通过import完成

// 执行主函数
main().catch(error => {
    console.error('❌ 执行失败:', error);
    process.exit(1);
});