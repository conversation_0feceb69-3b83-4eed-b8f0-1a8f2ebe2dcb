#!/usr/bin/env node

/**
 * 深度重构 packages/shared 包
 * 彻底清理复杂结构，建立简洁统一的架构
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔥 开始深度重构 packages/shared 包...\n');

const SHARED_PATH = 'packages/shared';

/**
 * 完全重建 shared 包
 */
function deepRefactorShared() {
    console.log('🗑️ 清理现有复杂结构');

    // 1. 备份package.json和README.md
    const packageJson = fs.readFileSync(path.join(SHARED_PATH, 'package.json'), 'utf8');
    const readme = fs.existsSync(path.join(SHARED_PATH, 'README.md'))
        ? fs.readFileSync(path.join(SHARED_PATH, 'README.md'), 'utf8')
        : '';

    // 2. 完全清空src目录
    const srcPath = path.join(SHARED_PATH, 'src');
    if (fs.existsSync(srcPath)) {
        execSync(`rm -rf "${srcPath}"`, { stdio: 'inherit' });
        console.log('🗑️ 清空src目录');
    }

    // 3. 创建新的简洁结构
    const newStructure = [
        'src',
        'src/constants',
        'src/types',
        'src/utils',
        'src/errors',
        '__tests__',
        '__tests__/unit',
        '__tests__/integration'
    ];

    newStructure.forEach(dir => {
        const fullPath = path.join(SHARED_PATH, dir);
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
    });

    // 4. 创建核心文件
    createCoreFiles();

    // 5. 恢复package.json
    fs.writeFileSync(path.join(SHARED_PATH, 'package.json'), packageJson);

    // 6. 创建新的README
    createReadme();

    // 7. 创建配置文件
    createConfigFiles();

    console.log('✅ 深度重构完成\n');
}

/**
 * 创建核心文件
 */
function createCoreFiles() {
    console.log('📝 创建核心文件');

    // 创建常量文件
    createConstants();

    // 创建类型文件
    createTypes();

    // 创建工具函数文件
    createUtils();

    // 创建错误处理文件
    createErrors();

    // 创建主入口文件
    createMainIndex();
}

/**
 * 创建常量文件
 */
function createConstants() {
    const content = `/**
 * Micro-Core 核心常量
 * @description 统一管理所有常量定义
 */

// 应用状态
export const APP_STATUS = {
    NOT_LOADED: 'NOT_LOADED',
    LOADING: 'LOADING',
    LOADED: 'LOADED',
    BOOTSTRAPPING: 'BOOTSTRAPPING',
    BOOTSTRAPPED: 'BOOTSTRAPPED',
    MOUNTING: 'MOUNTING',
    MOUNTED: 'MOUNTED',
    UNMOUNTING: 'UNMOUNTING',
    UNMOUNTED: 'UNMOUNTED',
    ERROR: 'ERROR'
} as const;

// 沙箱类型
export const SANDBOX_TYPES = {
    PROXY: 'proxy',
    IFRAME: 'iframe',
    WEB_COMPONENT: 'web-component',
    NAMESPACE: 'namespace'
} as const;

// 生命周期钩子
export const LIFECYCLE_HOOKS = {
    BEFORE_LOAD: 'beforeLoad',
    AFTER_LOAD: 'afterLoad',
    BEFORE_BOOTSTRAP: 'beforeBootstrap',
    AFTER_BOOTSTRAP: 'afterBootstrap',
    BEFORE_MOUNT: 'beforeMount',
    AFTER_MOUNT: 'afterMount',
    BEFORE_UNMOUNT: 'beforeUnmount',
    AFTER_UNMOUNT: 'afterUnmount'
} as const;

// 事件类型
export const EVENT_TYPES = {
    APP_REGISTERED: 'app:registered',
    APP_LOADED: 'app:loaded',
    APP_MOUNTED: 'app:mounted',
    APP_UNMOUNTED: 'app:unmounted',
    APP_ERROR: 'app:error',
    ROUTE_CHANGED: 'route:changed'
} as const;

// 错误码
export const ERROR_CODES = {
    SYSTEM_ERROR: 1000,
    APP_LOAD_ERROR: 2000,
    APP_MOUNT_ERROR: 2001,
    SANDBOX_ERROR: 3000,
    PLUGIN_ERROR: 4000,
    ROUTE_ERROR: 5000
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
    TIMEOUT: 30000,
    RETRY_COUNT: 3,
    SANDBOX_TYPE: SANDBOX_TYPES.PROXY
} as const;
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/constants/index.ts'), content);
    console.log('✅ 创建常量文件');
}

/**
 * 创建类型文件
 */
function createTypes() {
    const content = `/**
 * Micro-Core 核心类型定义
 * @description 统一管理所有类型
 */

import type { 
    APP_STATUS, 
    SANDBOX_TYPES, 
    LIFECYCLE_HOOKS, 
    EVENT_TYPES, 
    ERROR_CODES 
} from '../constants';

// 基础类型
export type AppStatus = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type LifecycleHook = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

// 应用配置
export interface AppConfig {
    name: string;
    entry: string;
    container: string | HTMLElement;
    activeWhen: string | ((location: Location) => boolean);
    props?: Record<string, any>;
    sandbox?: SandboxConfig;
}

// 沙箱配置
export interface SandboxConfig {
    type: SandboxType;
    strict?: boolean;
}

// 应用实例
export interface AppInstance {
    name: string;
    status: AppStatus;
    config: AppConfig;
    sandbox?: SandboxInstance;
}

// 沙箱实例
export interface SandboxInstance {
    name: string;
    type: SandboxType;
    active: boolean;
    activate(): Promise<void>;
    deactivate(): Promise<void>;
    destroy(): Promise<void>;
}

// 插件接口
export interface Plugin {
    name: string;
    version: string;
    install(kernel: MicroCoreKernel): Promise<void> | void;
    uninstall?(kernel: MicroCoreKernel): Promise<void> | void;
}

// 内核接口
export interface MicroCoreKernel {
    registerApp(config: AppConfig): Promise<void>;
    unregisterApp(name: string): Promise<void>;
    loadApp(name: string): Promise<void>;
    mountApp(name: string): Promise<void>;
    unmountApp(name: string): Promise<void>;
    getApp(name: string): AppInstance | undefined;
    use(plugin: Plugin): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
}

// 生命周期函数
export type LifecycleFn<T = any> = (app: AppInstance) => Promise<T> | T;

// 事件
export interface MicroCoreEvent {
    type: EventType;
    app?: AppInstance;
    data?: any;
    timestamp: number;
}

// 错误
export interface MicroCoreError extends Error {
    code: ErrorCode;
    app?: string;
    data?: Record<string, any>;
}

// 工具类型
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Awaitable<T> = T | Promise<T>;
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/types/index.ts'), content);
    console.log('✅ 创建类型文件');
}

/**
 * 创建工具函数文件
 */
function createUtils() {
    const content = `/**
 * Micro-Core 核心工具函数
 * @description 提供通用工具函数
 */

import type { AppConfig } from '../types';

// 类型检查
export const isString = (value: unknown): value is string => typeof value === 'string';
export const isFunction = (value: unknown): value is Function => typeof value === 'function';
export const isObject = (value: unknown): value is object => value !== null && typeof value === 'object';
export const isPromise = (value: unknown): value is Promise<any> => 
    isObject(value) && isFunction((value as any).then);

// 字符串工具
export function kebabCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

export function camelCase(str: string): string {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

// 对象工具
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as any;
    if (obj instanceof Array) return obj.map(item => deepClone(item)) as any;
    
    const cloned = {} as any;
    Object.keys(obj).forEach(key => {
        cloned[key] = deepClone((obj as any)[key]);
    });
    return cloned;
}

// 异步工具
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
        promise,
        new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error(\`Timeout after \${ms}ms\`)), ms)
        )
    ]);
}

// DOM工具
export function loadScript(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(\`Failed to load script: \${src}\`));
        document.head.appendChild(script);
    });
}

// URL工具
export function isAbsoluteUrl(url: string): boolean {
    return /^https?:\\/\\//.test(url);
}

// 验证工具
export function validateAppConfig(config: AppConfig): void {
    if (!config.name) throw new Error('应用名称不能为空');
    if (!config.entry) throw new Error('应用入口不能为空');
    if (!config.container) throw new Error('应用容器不能为空');
    if (!config.activeWhen) throw new Error('应用激活条件不能为空');
}

// 性能工具
export function measureTime<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(\`[\${name}] 执行时间: \${(end - start).toFixed(2)}ms\`);
    return result;
}

// 日志工具
export const logger = {
    debug: (message: string, ...args: any[]) => {
        if (process.env.NODE_ENV === 'development') {
            console.debug(\`[Micro-Core] \${message}\`, ...args);
        }
    },
    info: (message: string, ...args: any[]) => {
        console.info(\`[Micro-Core] \${message}\`, ...args);
    },
    warn: (message: string, ...args: any[]) => {
        console.warn(\`[Micro-Core] \${message}\`, ...args);
    },
    error: (message: string, ...args: any[]) => {
        console.error(\`[Micro-Core] \${message}\`, ...args);
    }
};
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/utils/index.ts'), content);
    console.log('✅ 创建工具函数文件');
}

/**
 * 创建错误处理文件
 */
function createErrors() {
    const content = `/**
 * Micro-Core 错误处理
 * @description 统一错误处理机制
 */

import type { MicroCoreError as IMicroCoreError, ErrorCode } from '../types';
import { ERROR_CODES } from '../constants';

/**
 * Micro-Core 自定义错误类
 */
export class MicroCoreError extends Error implements IMicroCoreError {
    public readonly code: ErrorCode;
    public readonly app?: string;
    public readonly data?: Record<string, any>;

    constructor(
        code: ErrorCode,
        message: string,
        app?: string,
        data?: Record<string, any>
    ) {
        super(message);
        this.name = 'MicroCoreError';
        this.code = code;
        this.app = app;
        this.data = data;

        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, MicroCoreError);
        }
    }

    static fromCode(code: ErrorCode, app?: string, data?: Record<string, any>): MicroCoreError {
        const message = ERROR_MESSAGES[code] || '未知错误';
        return new MicroCoreError(code, message, app, data);
    }

    toJSON(): object {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            app: this.app,
            data: this.data,
            stack: this.stack
        };
    }
}

/**
 * 错误消息映射
 */
const ERROR_MESSAGES: Record<ErrorCode, string> = {
    [ERROR_CODES.SYSTEM_ERROR]: '系统错误',
    [ERROR_CODES.APP_LOAD_ERROR]: '应用加载失败',
    [ERROR_CODES.APP_MOUNT_ERROR]: '应用挂载失败',
    [ERROR_CODES.SANDBOX_ERROR]: '沙箱错误',
    [ERROR_CODES.PLUGIN_ERROR]: '插件错误',
    [ERROR_CODES.ROUTE_ERROR]: '路由错误'
};

/**
 * 错误处理器
 */
export class ErrorHandler {
    private static handlers = new Map<ErrorCode, (error: MicroCoreError) => void>();

    static register(code: ErrorCode, handler: (error: MicroCoreError) => void): void {
        this.handlers.set(code, handler);
    }

    static handle(error: unknown): void {
        if (error instanceof MicroCoreError) {
            const handler = this.handlers.get(error.code);
            if (handler) {
                handler(error);
            } else {
                console.error(\`[Micro-Core Error \${error.code}] \${error.message}\`, error);
            }
        } else {
            console.error('[Micro-Core] 未知错误:', error);
        }
    }
}
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/errors/index.ts'), content);
    console.log('✅ 创建错误处理文件');
}

/**
 * 创建主入口文件
 */
function createMainIndex() {
    const content = `/**
 * @micro-core/shared
 * Micro-Core 共享工具包
 * 
 * @version 0.1.0
 * <AUTHOR> <<EMAIL>>
 */

// 导出常量
export * from './constants';

// 导出类型
export * from './types';

// 导出工具函数
export * from './utils';

// 导出错误处理
export * from './errors';

// 版本信息
export const VERSION = '0.1.0';
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'src/index.ts'), content);
    console.log('✅ 创建主入口文件');
}

/**
 * 创建README
 */
function createReadme() {
    const content = `# @micro-core/shared

Micro-Core 共享工具包，提供核心常量、类型定义、工具函数和错误处理。

## 特性

- 🎯 **统一常量** - 集中管理所有常量定义
- 🔧 **类型安全** - 完整的 TypeScript 类型支持
- 🛠️ **工具函数** - 常用工具函数集合
- ❌ **错误处理** - 统一的错误处理机制

## 安装

\`\`\`bash
pnpm add @micro-core/shared
\`\`\`

## 使用

\`\`\`typescript
import { APP_STATUS, AppConfig, logger, MicroCoreError } from '@micro-core/shared';

// 使用常量
console.log(APP_STATUS.MOUNTED);

// 使用类型
const config: AppConfig = {
    name: 'my-app',
    entry: 'http://localhost:3000',
    container: '#app',
    activeWhen: '/my-app'
};

// 使用工具函数
logger.info('应用启动');

// 使用错误处理
throw new MicroCoreError(2000, '应用加载失败');
\`\`\`

## API 文档

### 常量

- \`APP_STATUS\` - 应用状态常量
- \`SANDBOX_TYPES\` - 沙箱类型常量
- \`LIFECYCLE_HOOKS\` - 生命周期钩子常量
- \`EVENT_TYPES\` - 事件类型常量
- \`ERROR_CODES\` - 错误码常量

### 类型

- \`AppConfig\` - 应用配置接口
- \`AppInstance\` - 应用实例接口
- \`SandboxInstance\` - 沙箱实例接口
- \`Plugin\` - 插件接口
- \`MicroCoreKernel\` - 内核接口

### 工具函数

- \`isString\`, \`isFunction\`, \`isObject\` - 类型检查
- \`kebabCase\`, \`camelCase\` - 字符串转换
- \`deepClone\` - 深度克隆
- \`sleep\`, \`timeout\` - 异步工具
- \`loadScript\` - DOM 工具
- \`validateAppConfig\` - 验证工具
- \`logger\` - 日志工具

### 错误处理

- \`MicroCoreError\` - 自定义错误类
- \`ErrorHandler\` - 错误处理器

## 许可证

MIT
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'README.md'), content);
    console.log('✅ 创建README文件');
}

/**
 * 创建配置文件
 */
function createConfigFiles() {
    // TypeScript配置
    const tsConfig = {
        "extends": "../../tsconfig.base.json",
        "compilerOptions": {
            "outDir": "./dist",
            "rootDir": "./src"
        },
        "include": ["src/**/*"],
        "exclude": ["node_modules", "dist", "__tests__"]
    };

    fs.writeFileSync(
        path.join(SHARED_PATH, 'tsconfig.json'),
        JSON.stringify(tsConfig, null, 2)
    );

    // Vitest配置
    const vitestConfig = `import { defineConfig } from 'vitest/config';

export default defineConfig({
    test: {
        environment: 'jsdom',
        coverage: {
            provider: 'v8',
            reporter: ['text', 'json', 'html']
        }
    }
});
`;

    fs.writeFileSync(path.join(SHARED_PATH, 'vitest.config.ts'), vitestConfig);

    console.log('✅ 创建配置文件');
}

// 执行深度重构
deepRefactorShared();

console.log('🎉 packages/shared 深度重构完成！');
console.log('\n📋 重构成果:');
console.log('✅ 清理了所有复杂嵌套结构');
console.log('✅ 建立了简洁统一的目录架构');
console.log('✅ 遵循单一职责原则');
console.log('✅ 避免了目录深度过深的问题');
console.log('✅ 统一了所有核心功能模块');