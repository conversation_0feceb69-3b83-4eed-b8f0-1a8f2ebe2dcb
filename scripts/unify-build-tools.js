#!/usr/bin/env node

/**
 * 统一构建工具配置
 * 移除所有tsup配置，统一使用vite 7.0.6
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 开始统一构建工具配置...\n');

/**
 * 递归查找文件
 */
function findFiles(dir, pattern, results = []) {
    if (!fs.existsSync(dir)) return results;

    const files = fs.readdirSync(dir);

    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
            findFiles(filePath, pattern, results);
        } else if (file.match(pattern)) {
            results.push(filePath);
        }
    }

    return results;
}

/**
 * 获取所有包目录
 */
function getPackageDirs() {
    const packagesDir = 'packages';
    if (!fs.existsSync(packagesDir)) return [];

    return fs.readdirSync(packagesDir)
        .map(name => path.join(packagesDir, name))
        .filter(dir => fs.statSync(dir).isDirectory());
}

/**
 * 统一构建工具
 */
function unifyBuildTools() {
    console.log('📦 处理包配置');

    // 1. 移除所有tsup配置文件
    removeTsupConfigs();

    // 2. 更新所有package.json的构建脚本
    updatePackageJsons();

    // 3. 创建统一的vite配置
    createViteConfigs();

    // 4. 更新根目录配置
    updateRootConfig();

    console.log('✅ 构建工具统一完成\n');
}

/**
 * 移除tsup配置文件
 */
function removeTsupConfigs() {
    console.log('🗑️ 移除tsup配置文件');

    const tsupFiles = [
        'tsup.config.ts',
        'tsup.config.js',
        'tsup.config.mjs'
    ];

    // 查找所有tsup配置文件
    const allTsupFiles = [];
    tsupFiles.forEach(fileName => {
        const files = findFiles('.', new RegExp(fileName + '$'));
        allTsupFiles.push(...files);
    });

    // 备份并删除tsup配置文件
    allTsupFiles.forEach(file => {
        if (fs.existsSync(file)) {
            // 备份到_backup目录
            const backupPath = path.join('_backup/tsup-configs', file);
            const backupDir = path.dirname(backupPath);

            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, { recursive: true });
            }

            fs.copyFileSync(file, backupPath);
            fs.unlinkSync(file);
            console.log(`🗑️ 移除并备份: ${file}`);
        }
    });

    console.log('✅ tsup配置文件清理完成');
}

/**
 * 更新package.json构建脚本
 */
function updatePackageJsons() {
    console.log('📝 更新package.json构建脚本');

    const packagePaths = getPackageDirs()
        .map(dir => path.join(dir, 'package.json'))
        .filter(packagePath => fs.existsSync(packagePath));

    packagePaths.forEach(packagePath => {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const packageName = packageJson.name;

        // 更新构建脚本
        packageJson.scripts = {
            ...packageJson.scripts,
            build: 'vite build',
            dev: 'vite build --watch',
            preview: 'vite preview',
            'type-check': 'tsc --noEmit'
        };

        // 更新依赖
        if (packageJson.devDependencies) {
            // 移除tsup相关依赖
            delete packageJson.devDependencies.tsup;
            delete packageJson.devDependencies['@types/tsup'];

            // 确保vite依赖
            packageJson.devDependencies.vite = '^7.0.6';
            packageJson.devDependencies['vite-plugin-dts'] = '^4.3.0';
        }

        // 更新主入口字段
        packageJson.main = './dist/index.js';
        packageJson.module = './dist/index.mjs';
        packageJson.types = './dist/index.d.ts';
        packageJson.exports = {
            '.': {
                import: './dist/index.mjs',
                require: './dist/index.js',
                types: './dist/index.d.ts'
            }
        };

        fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');
        console.log(`✅ 更新构建脚本: ${packageName}`);
    });

    console.log('✅ package.json更新完成');
}

/**
 * 创建vite配置文件
 */
function createViteConfigs() {
    console.log('⚙️ 创建vite配置文件');

    const packageDirs = getPackageDirs();

    packageDirs.forEach(packageDir => {
        const packageName = path.basename(packageDir);
        const viteConfigPath = path.join(packageDir, 'vite.config.ts');

        const viteConfig = createViteConfigContent(packageName);
        fs.writeFileSync(viteConfigPath, viteConfig);
        console.log(`✅ 创建vite配置: ${packageName}`);
    });

    console.log('✅ vite配置文件创建完成');
}

/**
 * 创建vite配置内容
 */
function createViteConfigContent(packageName) {
    return `import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        dts({
            insertTypesEntry: true,
            rollupTypes: true
        })
    ],
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: '${packageName.split('-').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join('')}',
            formats: ['es', 'cjs'],
            fileName: (format) => \`index.\${format === 'es' ? 'mjs' : 'js'}\`
        },
        rollupOptions: {
            external: [
                'react',
                'react-dom',
                'vue',
                '@vue/runtime-core',
                'angular'
            ],
            output: {
                globals: {
                    'react': 'React',
                    'react-dom': 'ReactDOM',
                    'vue': 'Vue'
                }
            }
        },
        sourcemap: true,
        minify: 'esbuild'
    },
    define: {
        __VERSION__: JSON.stringify(process.env.npm_package_version || '0.1.0')
    }
});
`;
}

/**
 * 更新根目录配置
 */
function updateRootConfig() {
    console.log('🔧 更新根目录配置');

    // 更新根package.json
    const rootPackagePath = 'package.json';
    const rootPackage = JSON.parse(fs.readFileSync(rootPackagePath, 'utf8'));

    // 更新脚本
    rootPackage.scripts = {
        ...rootPackage.scripts,
        build: 'turbo run build',
        'build:packages': 'turbo run build --filter="./packages/*"',
        dev: 'turbo run dev --parallel',
        'type-check': 'turbo run type-check',
        clean: 'turbo run clean && rm -rf dist',
        'clean:deps': 'rm -rf node_modules packages/*/node_modules'
    };

    // 更新依赖
    if (rootPackage.devDependencies) {
        // 移除tsup
        delete rootPackage.devDependencies.tsup;

        // 确保vite和相关工具
        rootPackage.devDependencies.vite = '^7.0.6';
        rootPackage.devDependencies['vite-plugin-dts'] = '^4.3.0';
        rootPackage.devDependencies.turbo = '^2.3.0';
    }

    fs.writeFileSync(rootPackagePath, JSON.stringify(rootPackage, null, 2) + '\n');
    console.log('✅ 更新根目录package.json');

    // 更新turbo.json
    updateTurboConfig();

    console.log('✅ 根目录配置更新完成');
}

/**
 * 更新turbo配置
 */
function updateTurboConfig() {
    const turboConfig = {
        "$schema": "https://turbo.build/schema.json",
        "pipeline": {
            "build": {
                "dependsOn": ["^build"],
                "outputs": ["dist/**"],
                "env": ["NODE_ENV"]
            },
            "dev": {
                "cache": false,
                "persistent": true
            },
            "type-check": {
                "dependsOn": ["^build"],
                "outputs": []
            },
            "test": {
                "dependsOn": ["build"],
                "outputs": ["coverage/**"]
            },
            "lint": {
                "outputs": []
            },
            "clean": {
                "cache": false
            }
        },
        "globalDependencies": [
            "package.json",
            "tsconfig.json",
            "turbo.json"
        ]
    };

    fs.writeFileSync('turbo.json', JSON.stringify(turboConfig, null, 2) + '\n');
    console.log('✅ 更新turbo.json');
}

// 执行统一构建工具配置
unifyBuildTools();

console.log('🎉 构建工具统一完成！');
console.log('\n📋 统一成果:');
console.log('✅ 移除所有tsup配置文件');
console.log('✅ 统一使用vite 7.0.6构建');
console.log('✅ 更新所有package.json构建脚本');
console.log('✅ 创建标准化vite配置文件');
console.log('✅ 优化turbo构建流水线');
console.log('✅ 确保构建产物一致性');