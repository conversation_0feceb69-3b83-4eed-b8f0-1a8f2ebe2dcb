# 工具函数迁移指南

## 概述

为了优化包结构和减少代码重复，我们将核心工具函数从 `@micro-core/core` 包迁移到了 `@micro-core/shared/utils` 包中。本文档提供了迁移的详细信息和使用指南。

## 已迁移的工具函数

### 类型检查工具

以下类型检查工具函数已迁移到 `@micro-core/shared/utils/type-check`：

- `isObject` - 检查值是否为对象
- `isFunction` - 检查值是否为函数
- `isString` - 检查值是否为字符串
- `isNumber` - 检查值是否为数字
- `isBoolean` - 检查值是否为布尔值
- `isArray` - 检查值是否为数组
- `isPromise` - 检查值是否为Promise
- `isEmpty` - 检查值是否为空

### URL工具

以下URL工具函数已迁移到 `@micro-core/shared/utils/url`：

- `isValidUrl` - 验证URL是否有效

### 日志工具

以下日志工具函数已迁移到 `@micro-core/shared/utils/logger`：

- `createLogger` - 创建日志记录器

### 格式化工具

以下格式化工具函数已迁移到 `@micro-core/shared/utils/format`：

- `formatBytes` - 格式化字节大小
- `formatTime` - 格式化时间
- `formatError` - 格式化错误信息

### ID生成工具

以下ID生成工具函数已迁移到 `@micro-core/shared/utils/id`：

- `generateId` - 生成微前端应用唯一ID

## 如何迁移

### 旧的导入方式

```typescript
import { isObject, isFunction, createLogger, formatError } from '@micro-core/core';
```

### 新的导入方式

```typescript
import { isObject, isFunction, createLogger, formatError } from '@micro-core/shared/utils';
```

## 向后兼容性

为了确保向后兼容性，`@micro-core/core` 包中的工具函数仍然可用，但会在开发环境中显示废弃警告。我们建议尽快迁移到新的导入方式。

## 增强功能

在迁移过程中，我们对一些工具函数进行了增强：

### formatError

- 增加了对非序列化对象的处理
- 增加了对错误对象额外属性的处理
- 增加了对上下文信息的处理

### createLogger

- 增加了对日志级别的支持
- 增加了对时间戳的支持
- 增加了对颜色的支持

## 测试

所有迁移的工具函数都有完整的单元测试，确保功能正常。测试文件位于 `@micro-core/shared/utils/__tests__` 目录中。

## 下一步

我们将继续迁移更多的工具函数，并增强现有功能。如有任何问题或建议，请联系项目维护者。