{"name": "@micro-core/shared", "version": "0.1.0", "description": "Micro-Core 共享工具包 - 提供通用工具函数、类型定义、配置管理和开发工具链支持", "keywords": ["micro-core", "shared", "utils", "types", "config", "eslint-config", "typescript-config", "vitest-config", "development-tools"], "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/shared"}, "license": "MIT", "author": {"name": "Echo", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./constants": {"types": "./dist/constants/index.d.ts", "import": "./dist/constants/index.js", "require": "./dist/constants/index.cjs"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.cjs"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.cjs"}, "./helpers": {"types": "./dist/helpers/index.d.ts", "import": "./dist/helpers/index.js", "require": "./dist/helpers/index.cjs"}, "./eslint-config": {"types": "./eslint-config/index.d.ts", "import": "./eslint-config/index.js", "require": "./eslint-config/index.js"}, "./ts-config": {"types": "./ts-config/index.d.ts", "import": "./ts-config/index.js", "require": "./ts-config/index.js"}, "./vitest-config": {"types": "./vitest-config/index.d.ts", "import": "./vitest-config/index.js", "require": "./vitest-config/index.js"}}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "constants", "types", "utils", "helpers", "eslint-config", "ts-config", "vitest-config", "prettier-config", "jest-config", "README.md", "CHANGELOG.md"], "scripts": {"clean": "rm -rf dist coverage test-results", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:run": "vitest run", "test:integration": "vitest run --config vitest.integration.config.ts", "test:performance": "vitest run --config vitest.performance.config.ts", "type-check": "tsc --noEmit", "size-check": "bundlesize", "validate": "pnpm run type-check && pnpm run lint && pnpm run test:run", "ci": "pnpm run clean && pnpm run build && pnpm run validate && pnpm run test:coverage", "prepublishOnly": "pnpm run ci", "docs:build": "typedoc --out docs src", "docs:serve": "serve docs", "build": "vite build", "dev": "vite build --watch", "test:watch": "vitest"}, "dependencies": {}, "devDependencies": {"@types/node": "^20.0.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "bundlesize": "^0.18.0", "eslint": "^8.57.0", "jsdom": "^24.0.0", "serve": "^14.0.0", "typedoc": "^0.25.0", "typescript": "^5.3.3", "vitest": "^3.2.4"}, "bundlesize": [{"path": "./dist/index.js", "maxSize": "25kb", "compression": "gzip"}, {"path": "./dist/utils/index.js", "maxSize": "15kb", "compression": "gzip"}, {"path": "./dist/types/index.js", "maxSize": "5kb", "compression": "gzip"}, {"path": "./dist/constants/index.js", "maxSize": "3kb", "compression": "gzip"}], "publishConfig": {"access": "public"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}}