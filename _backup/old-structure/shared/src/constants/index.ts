/**
 * Constants for Micro-Core
 */

/**
 * Application status constants
 */
export const APP_STATUS = {
    NOT_LOADED: 'NOT_LOADED',
    LOADING: 'LOADING',
    LOADED: 'LOADED',
    MOUNTING: 'MOUNTING',
    MOUNTED: 'MOUNTED',
    UNMOUNTING: 'UNMOUNTING',
    UNMOUNTED: 'UNMOUNTED',
    ERROR: 'ERROR'
} as const;

/**
 * Error codes
 */
export const ERROR_CODES = {
    // General errors
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    INVALID_ARGUMENT: 'INVALID_ARGUMENT',
    OPERATION_FAILED: 'OPERATION_FAILED',

    // Application errors
    APPLICATION_ERROR: 'APPLICATION_ERROR',
    APP_NOT_FOUND: 'APP_NOT_FOUND',
    APP_ALREADY_REGISTERED: 'APP_ALREADY_REGISTERED',
    APP_LOAD_FAILED: 'APP_LOAD_FAILED',
    APP_MOUNT_FAILED: 'APP_MOUNT_FAILED',
    APP_UNMOUNT_FAILED: 'APP_UNMOUNT_FAILED',
    APP_UPDATE_FAILED: 'APP_UPDATE_FAILED',

    // Plugin errors
    PLUGIN_ERROR: 'PLUGIN_ERROR',
    PLUGIN_NOT_FOUND: 'PLUGIN_NOT_FOUND',
    PLUGIN_ALREADY_REGISTERED: 'PLUGIN_ALREADY_REGISTERED',
    PLUGIN_INSTALL_FAILED: 'PLUGIN_INSTALL_FAILED',
    PLUGIN_UNINSTALL_FAILED: 'PLUGIN_UNINSTALL_FAILED',
    PLUGIN_DEPENDENCY_NOT_FOUND: 'PLUGIN_DEPENDENCY_NOT_FOUND',

    // Sandbox errors
    SANDBOX_ERROR: 'SANDBOX_ERROR',
    SANDBOX_CREATE_FAILED: 'SANDBOX_CREATE_FAILED',
    SANDBOX_DESTROY_FAILED: 'SANDBOX_DESTROY_FAILED',
    SANDBOX_EXEC_FAILED: 'SANDBOX_EXEC_FAILED',
    INVALID_SANDBOX_TYPE: 'INVALID_SANDBOX_TYPE',

    // Resource errors
    RESOURCE_ERROR: 'RESOURCE_ERROR',
    RESOURCE_LOAD_FAILED: 'RESOURCE_LOAD_FAILED',
    RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
    RESOURCE_TIMEOUT: 'RESOURCE_TIMEOUT',

    // Communication errors
    COMMUNICATION_ERROR: 'COMMUNICATION_ERROR',
    COMMUNICATION_FAILED: 'COMMUNICATION_FAILED',
    EVENT_HANDLER_ERROR: 'EVENT_HANDLER_ERROR',
    EVENT_EMISSION_FAILED: 'EVENT_EMISSION_FAILED',
    INVALID_EVENT_NAME: 'INVALID_EVENT_NAME',
    STATE_UPDATE_FAILED: 'STATE_UPDATE_FAILED',

    // Router errors
    ROUTER_ERROR: 'ROUTER_ERROR',
    ROUTE_NOT_FOUND: 'ROUTE_NOT_FOUND',
    NAVIGATION_FAILED: 'NAVIGATION_FAILED',

    // Lifecycle errors
    INVALID_LIFECYCLE_STATE: 'INVALID_LIFECYCLE_STATE',
    LIFECYCLE_TIMEOUT: 'LIFECYCLE_TIMEOUT'
} as const;

/**
 * Plugin types
 */
export const PLUGIN_TYPES = {
    ROUTER: 'router',
    SANDBOX: 'sandbox',
    COMMUNICATION: 'communication',
    AUTH: 'auth',
    PREFETCH: 'prefetch',
    LOADER: 'loader',
    DEVTOOLS: 'devtools',
    LOGGER: 'logger',
    METRICS: 'metrics',
    COMPAT: 'compat'
} as const;

/**
 * Sandbox types
 */
export const SANDBOX_TYPES = {
    PROXY: 'proxy',
    DEFINE_PROPERTY: 'defineProperty',
    WEB_COMPONENT: 'webComponent',
    IFRAME: 'iframe',
    NAMESPACE: 'namespace',
    FEDERATION: 'federation'
} as const;

/**
 * Router modes
 */
export const ROUTER_MODES = {
    HISTORY: 'history',
    HASH: 'hash',
    MEMORY: 'memory'
} as const;

/**
 * Resource types
 */
export const RESOURCE_TYPES = {
    JS: 'js',
    CSS: 'css',
    HTML: 'html',
    JSON: 'json',
    WASM: 'wasm'
} as const;

/**
 * Event names
 */
export const EVENTS = {
    // Application events
    APP_REGISTERED: 'app:registered',
    APP_UNREGISTERED: 'app:unregistered',
    APP_LOADING: 'app:loading',
    APP_LOADED: 'app:loaded',
    APP_MOUNTING: 'app:mounting',
    APP_MOUNTED: 'app:mounted',
    APP_UNMOUNTING: 'app:unmounting',
    APP_UNMOUNTED: 'app:unmounted',
    APP_ERROR: 'app:error',

    // Plugin events
    PLUGIN_INSTALLED: 'plugin:installed',
    PLUGIN_UNINSTALLED: 'plugin:uninstalled',
    PLUGIN_ERROR: 'plugin:error',

    // Router events
    ROUTE_CHANGED: 'route:changed',
    NAVIGATION_START: 'navigation:start',
    NAVIGATION_END: 'navigation:end',
    NAVIGATION_ERROR: 'navigation:error',

    // Sandbox events
    SANDBOX_CREATED: 'sandbox:created',
    SANDBOX_DESTROYED: 'sandbox:destroyed',
    SANDBOX_ACTIVATED: 'sandbox:activated',
    SANDBOX_DEACTIVATED: 'sandbox:deactivated',

    // Resource events
    RESOURCE_LOADING: 'resource:loading',
    RESOURCE_LOADED: 'resource:loaded',
    RESOURCE_ERROR: 'resource:error',

    // Communication events
    STATE_CHANGED: 'state:changed',
    MESSAGE_SENT: 'message:sent',
    MESSAGE_RECEIVED: 'message:received'
} as const;

/**
 * Default configuration values
 */
export const DEFAULTS = {
    TIMEOUT: 30000, // 30 seconds
    RETRY_COUNT: 3,
    DEBOUNCE_DELAY: 300,
    THROTTLE_LIMIT: 100,
    MAX_LISTENERS: 10,
    CACHE_SIZE: 100,
    LOG_LEVEL: 'info'
} as const;

/**
 * Framework types
 */
export const FRAMEWORK_TYPES = {
    REACT: 'react',
    VUE2: 'vue2',
    VUE3: 'vue3',
    ANGULAR: 'angular',
    SVELTE: 'svelte',
    SOLID: 'solid',
    HTML: 'html'
} as const;

/**
 * Build tool types
 */
export const BUILD_TOOLS = {
    VITE: 'vite',
    WEBPACK: 'webpack',
    ROLLUP: 'rollup',
    ESBUILD: 'esbuild',
    RSPACK: 'rspack',
    PARCEL: 'parcel',
    TURBOPACK: 'turbopack'
} as const;

/**
 * Environment types
 */
export const ENVIRONMENTS = {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
    TEST: 'test'
} as const;

/**
 * Log levels
 */
export const LOG_LEVELS = {
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error'
} as const;

/**
 * HTTP methods
 */
export const HTTP_METHODS = {
    GET: 'GET',
    POST: 'POST',
    PUT: 'PUT',
    DELETE: 'DELETE',
    PATCH: 'PATCH',
    HEAD: 'HEAD',
    OPTIONS: 'OPTIONS'
} as const;

/**
 * Content types
 */
export const CONTENT_TYPES = {
    JSON: 'application/json',
    HTML: 'text/html',
    CSS: 'text/css',
    JS: 'application/javascript',
    TEXT: 'text/plain',
    XML: 'application/xml'
} as const;

/**
 * Cache strategies
 */
export const CACHE_STRATEGIES = {
    MEMORY: 'memory',
    SESSION: 'session',
    LOCAL: 'local',
    NONE: 'none'
} as const;

/**
 * Prefetch strategies
 */
export const PREFETCH_STRATEGIES = {
    ROUTE_PREDICTION: 'route-prediction',
    VIEWPORT_DETECTION: 'viewport-detection',
    MANUAL: 'manual',
    IDLE: 'idle'
} as const;

/**
 * Version information
 */
export const VERSION = '0.1.0';

/**
 * Package name
 */
export const PACKAGE_NAME = '@micro-core/core';

/**
 * Default user agent
 */
export const USER_AGENT = `${PACKAGE_NAME}/${VERSION}`;