# @micro-core/shared-constants

Constants and enums for Micro-Core projects.

## Installation

```bash
npm install @micro-core/shared-constants
```

## Usage

```typescript
import { 
  AppStatus,
  LogLevel,
  TaskStatus,
  EVENTS,
  ERRORS,
  REGEX,
  TIMEOUTS,
  LIMITS
} from '@micro-core/shared-constants';

// App status
console.log(AppStatus.LOADING); // 'loading'
console.log(AppStatus.MOUNTED); // 'mounted'

// Log levels
console.log(LogLevel.INFO); // 'info'
console.log(LogLevel.ERROR); // 'error'

// Events
console.log(EVENTS.APP_MOUNT); // 'app:mount'
console.log(EVENTS.APP_UNMOUNT); // 'app:unmount'

// Error codes
console.log(ERRORS.APP_LOAD_FAILED); // 'APP_LOAD_FAILED'

// Regular expressions
console.log(REGEX.EMAIL.test('<EMAIL>')); // true
console.log(REGEX.URL.test('https://example.com')); // true

// Timeouts
console.log(TIMEOUTS.DEFAULT); // 5000
console.log(TIMEOUTS.LONG); // 30000

// Limits
console.log(LIMITS.MAX_APPS); // 50
console.log(LIMITS.MAX_RETRIES); // 3
```

## API Reference

### Enums

#### AppStatus
Application lifecycle status constants:
- `LOADING` - Application is loading
- `LOADED` - Application has been loaded
- `BOOTSTRAPPING` - Application is bootstrapping
- `NOT_BOOTSTRAPPED` - Application failed to bootstrap
- `BOOTSTRAPPED` - Application has been bootstrapped
- `MOUNTING` - Application is mounting
- `NOT_MOUNTED` - Application failed to mount
- `MOUNTED` - Application has been mounted
- `UPDATING` - Application is updating
- `UNMOUNTING` - Application is unmounting
- `UNLOADED` - Application has been unloaded
- `LOAD_ERROR` - Application failed to load
- `SKIP_BECAUSE_BROKEN` - Application is broken and skipped

#### LogLevel
Logging level constants:
- `DEBUG` - Debug level logging
- `INFO` - Information level logging
- `WARN` - Warning level logging
- `ERROR` - Error level logging

#### TaskStatus
Task execution status constants:
- `PENDING` - Task is pending
- `RUNNING` - Task is running
- `COMPLETED` - Task completed successfully
- `FAILED` - Task failed
- `CANCELLED` - Task was cancelled

### Constants

#### EVENTS
Event name constants for micro-frontend communication:
- `APP_MOUNT` - Application mount event
- `APP_UNMOUNT` - Application unmount event
- `APP_ERROR` - Application error event
- `ROUTE_CHANGE` - Route change event
- `STATE_CHANGE` - State change event

#### ERRORS
Error code constants:
- `APP_LOAD_FAILED` - Application failed to load
- `APP_MOUNT_FAILED` - Application failed to mount
- `INVALID_CONFIG` - Invalid configuration
- `NETWORK_ERROR` - Network error
- `TIMEOUT_ERROR` - Timeout error

#### REGEX
Common regular expression patterns:
- `EMAIL` - Email validation pattern
- `URL` - URL validation pattern
- `PHONE` - Phone number validation pattern
- `ID_CARD` - ID card validation pattern

#### TIMEOUTS
Timeout constants (in milliseconds):
- `DEFAULT` - Default timeout (5000ms)
- `SHORT` - Short timeout (2000ms)
- `LONG` - Long timeout (30000ms)
- `VERY_LONG` - Very long timeout (60000ms)

#### LIMITS
Limit constants:
- `MAX_APPS` - Maximum number of applications (50)
- `MAX_RETRIES` - Maximum retry attempts (3)
- `MAX_CACHE_SIZE` - Maximum cache size (100MB)
- `MAX_LOG_SIZE` - Maximum log size (10MB)

## Features

- ✅ Full TypeScript support
- ✅ Tree-shakeable ESM modules
- ✅ CommonJS compatibility
- ✅ Comprehensive constants coverage
- ✅ Well-documented API
- ✅ Zero dependencies

## License

MIT
