
/**
 * @fileoverview 核心常量定义
 * @description 定义微前端框架核心功能所需的常量，包括应用状态、沙箱类型、资源类型等
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 应用状态常量
 * @description 定义微前端应用在生命周期中的各种状态
 */
export const APP_STATUS = {
    NOT_LOADED: 'NOT_LOADED',
    LOADING_SOURCE_CODE: 'LOADING_SOURCE_CODE',
    NOT_BOOTSTRAPPED: 'NOT_BOOTSTRAPPED',
    BOOTSTRAPPING: 'BOOTSTRAPPING',
    NOT_MOUNTED: 'NOT_MOUNTED',
    MOUNTING: 'MOUNTING',
    MOUNTED: 'MOUNTED',
    UPDATING: 'UPDATING',
    UNMOUNTING: 'UNMOUNTING',
    UNMOUNTED: 'UNMOUNTED',
    LOADED: 'LOADED',
    UNLOADING: 'UNLOADING',
    LOAD_ERROR: 'LOAD_ERROR',
    SKIP_BECAUSE_BROKEN: 'SKIP_BECAUSE_BROKEN'
} as const;

// ERROR_CODES 已移至 errors.ts 文件中，避免重复定义
// 如需使用错误码，请从 './errors' 导入

/**
 * 沙箱类型常量
 * @description 定义微前端框架支持的沙箱类型
 */
export const SANDBOX_TYPES = {
    /** 代理沙箱 */
    PROXY: 'proxy',
    /** 属性定义沙箱 */
    DEFINE_PROPERTY: 'define-property',
    /** Web Component 沙箱 */
    WEB_COMPONENT: 'web-component',
    /** iframe 沙箱 */
    IFRAME: 'iframe',
    /** 命名空间沙箱 */
    NAMESPACE: 'namespace',
    /** 模块联邦沙箱 */
    FEDERATION: 'federation'
} as const;

/**
 * 向后兼容的沙箱类型导出
 * @deprecated 请使用 SANDBOX_TYPES，此导出仅为向后兼容保留
 */
export const SANDBOX_TYPE = SANDBOX_TYPES;

/**
 * 向后兼容的沙箱类型导出
 * @deprecated 请使用 SANDBOX_TYPES，此导出仅为向后兼容保留
 */
export const SandboxType = SANDBOX_TYPES;

/**
 * 核心特有的资源类型枚举
 * @description 微前端应用资源类型定义
 */
export const RESOURCE_TYPE = {
    /** JavaScript 脚本 */
    SCRIPT: 'script',
    /** CSS 样式 */
    STYLE: 'style',
    /** HTML 模板 */
    HTML: 'html',
    /** 图片资源 */
    IMAGE: 'image',
    /** 字体资源 */
    FONT: 'font',
    /** 其他资源 */
    OTHER: 'other'
} as const;

/**
 * 核心特有的资源状态枚举
 * @description 微前端应用资源加载状态
 */
export const RESOURCE_STATUS = {
    /** 未加载 */
    NOT_LOADED: 'NOT_LOADED',
    /** 正在加载 */
    LOADING: 'LOADING',
    /** 已加载 */
    LOADED: 'LOADED',
    /** 加载失败 */
    LOAD_FAILED: 'LOAD_FAILED',
    /** 已缓存 */
    CACHED: 'CACHED'
} as const;

/**
 * 日志级别枚举 - 核心特有
 */
export const LOG_LEVEL = {
    /** 调试 */
    DEBUG: 'DEBUG',
    /** 信息 */
    INFO: 'INFO',
    /** 警告 */
    WARN: 'WARN',
    /** 错误 */
    ERROR: 'ERROR',
    /** 静默 */
    SILENT: 'SILENT'
} as const;

/**
 * 核心特有的生命周期钩子名称
 * @description 微前端应用生命周期钩子定义
 */
export const LIFECYCLE_HOOKS = {
    /** 启动前 */
    BEFORE_BOOTSTRAP: 'beforeBootstrap',
    /** 启动后 */
    AFTER_BOOTSTRAP: 'afterBootstrap',
    /** 挂载前 */
    BEFORE_MOUNT: 'beforeMount',
    /** 挂载后 */
    AFTER_MOUNT: 'afterMount',
    /** 卸载前 */
    BEFORE_UNMOUNT: 'beforeUnmount',
    /** 卸载后 */
    AFTER_UNMOUNT: 'afterUnmount',
    /** 更新前 */
    BEFORE_UPDATE: 'beforeUpdate',
    /** 更新后 */
    AFTER_UPDATE: 'afterUpdate'
} as const;

/**
 * 核心特有的事件名称常量
 * @description 微前端内核和插件系统特有的事件
 */
export const EVENT_NAMES = {
    /** 内核启动中 */
    KERNEL_STARTING: 'kernel:starting',
    /** 内核已启动 */
    KERNEL_STARTED: 'kernel:started',
    /** 内核停止中 */
    KERNEL_STOPPING: 'kernel:stopping',
    /** 内核已停止 */
    KERNEL_STOPPED: 'kernel:stopped',
    /** 内核销毁中 */
    KERNEL_DESTROYING: 'kernel:destroying',
    /** 内核已销毁 */
    KERNEL_DESTROYED: 'kernel:destroyed',
    /** 应用已注册 */
    APP_REGISTERED: 'app:registered',
    /** 应用已注销 */
    APP_UNREGISTERED: 'app:unregistered',
    /** 应用已加载 */
    APP_LOADED: 'app:loaded',
    /** 应用已挂载 */
    APP_MOUNTED: 'app:mounted',
    /** 应用已卸载 */
    APP_UNMOUNTED: 'app:unmounted',
    /** 应用错误 */
    APP_ERROR: 'app:error',
    /** 插件已安装 */
    PLUGIN_INSTALLED: 'plugin:installed',
    /** 插件已卸载 */
    PLUGIN_UNINSTALLED: 'plugin:uninstalled',
    /** 路由变化 */
    ROUTE_CHANGED: 'route:changed',
    /** 状态变化 */
    STATE_CHANGED: 'state:changed'
} as const;

/**
 * 核心特有的默认配置常量
 * @description 微前端内核默认配置
 */
export const DEFAULT_CONFIG = {
    /** 默认日志级别 */
    LOG_LEVEL: LOG_LEVEL.INFO,
    /** 默认沙箱类型 */
    SANDBOX_TYPE: SANDBOX_TYPES.PROXY,
    /** 默认生命周期超时时间（毫秒） */
    LIFECYCLE_TIMEOUT: 15000,
    /** 默认资源加载超时时间（毫秒） */
    RESOURCE_TIMEOUT: 10000
} as const;

/**
 * 类型导出 - 核心特有类型
 * @description 从常量对象中提取的联合类型，用于类型检查和约束
 */
export type AppStatusType = typeof APP_STATUS[keyof typeof APP_STATUS];
export type SandboxTypeType = typeof SANDBOX_TYPES[keyof typeof SANDBOX_TYPES];
export type ResourceTypeType = typeof RESOURCE_TYPE[keyof typeof RESOURCE_TYPE];
export type ResourceStatusType = typeof RESOURCE_STATUS[keyof typeof RESOURCE_STATUS];
export type LogLevelType = typeof LOG_LEVEL[keyof typeof LOG_LEVEL];
export type LifecycleHookType = typeof LIFECYCLE_HOOKS[keyof typeof LIFECYCLE_HOOKS];
export type EventNameType = typeof EVENT_NAMES[keyof typeof EVENT_NAMES];

// ErrorCodeType 已移至 errors.ts 文件中
