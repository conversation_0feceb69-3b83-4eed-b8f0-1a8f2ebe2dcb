/**
 * @fileoverview 常量定义测试
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { describe, it, expect } from 'vitest';
import {
    AppStatus,
    ErrorCodes,
    EventTypes,
    HTTP_STATUS,
    CONFIG,
    REGEX,
    MIME_TYPES,
    BROWSER_FEATURES,
    DEFAULT_APP_CONFIG,
    ENV_KEYS,
    STORAGE_KEYS,
    CSS_PREFIXES,
    DATA_ATTRIBUTES,
    META_KEYS,
    constants,
    // 向后兼容的导出
    SharedErrorCodes,
    SharedEventTypes,
    SHARED_CONFIG
} from '../src/index';

describe('常量定义', () => {
    describe('AppStatus', () => {
        it('应该包含所有应用状态', () => {
            expect(AppStatus.NOT_LOADED).toBe('NOT_LOADED');
            expect(AppStatus.LOADING_SOURCE_CODE).toBe('LOADING_SOURCE_CODE');
            expect(AppStatus.NOT_BOOTSTRAPPED).toBe('NOT_BOOTSTRAPPED');
            expect(AppStatus.BOOTSTRAPPING).toBe('BOOTSTRAPPING');
            expect(AppStatus.NOT_MOUNTED).toBe('NOT_MOUNTED');
            expect(AppStatus.MOUNTING).toBe('MOUNTING');
            expect(AppStatus.MOUNTED).toBe('MOUNTED');
            expect(AppStatus.UNMOUNTING).toBe('UNMOUNTING');
            expect(AppStatus.UPDATING).toBe('UPDATING');
            expect(AppStatus.LOAD_ERROR).toBe('LOAD_ERROR');
            expect(AppStatus.SKIP_BECAUSE_BROKEN).toBe('SKIP_BECAUSE_BROKEN');
        });
    });

    describe('ErrorCodes', () => {
        it('应该包含系统级错误代码', () => {
            expect(ErrorCodes.SYSTEM_INIT_FAILED).toBe(1000);
            expect(ErrorCodes.SYSTEM_CONFIG_ERROR).toBe(1001);
            expect(ErrorCodes.SYSTEM_RESOURCE_ERROR).toBe(1002);
        });

        it('应该包含应用级错误代码', () => {
            expect(ErrorCodes.APP_LOAD_FAILED).toBe(2000);
            expect(ErrorCodes.APP_BOOTSTRAP_FAILED).toBe(2001);
            expect(ErrorCodes.APP_MOUNT_FAILED).toBe(2002);
            expect(ErrorCodes.APP_UNMOUNT_FAILED).toBe(2003);
            expect(ErrorCodes.APP_UPDATE_FAILED).toBe(2004);
            expect(ErrorCodes.APP_CONFIG_INVALID).toBe(2005);
        });

        it('应该包含网络级错误代码', () => {
            expect(ErrorCodes.NETWORK_CONNECTION_FAILED).toBe(3000);
            expect(ErrorCodes.NETWORK_TIMEOUT).toBe(3001);
            expect(ErrorCodes.RESOURCE_LOAD_FAILED).toBe(3002);
            expect(ErrorCodes.CORS_ERROR).toBe(3003);
        });

        it('应该包含权限级错误代码', () => {
            expect(ErrorCodes.PERMISSION_DENIED).toBe(4000);
            expect(ErrorCodes.AUTHENTICATION_FAILED).toBe(4001);
            expect(ErrorCodes.AUTHORIZATION_FAILED).toBe(4002);
        });

        it('应该包含验证级错误代码', () => {
            expect(ErrorCodes.VALIDATION_FAILED).toBe(5000);
            expect(ErrorCodes.DATA_FORMAT_ERROR).toBe(5001);
            expect(ErrorCodes.TYPE_CHECK_FAILED).toBe(5002);
        });
    });

    describe('EventTypes', () => {
        it('应该包含系统事件', () => {
            expect(EventTypes.SYSTEM_INIT_START).toBe('system:init:start');
            expect(EventTypes.SYSTEM_INIT_COMPLETE).toBe('system:init:complete');
            expect(EventTypes.SYSTEM_INIT_FAILED).toBe('system:init:failed');
            expect(EventTypes.SYSTEM_DESTROY).toBe('system:destroy');
            expect(EventTypes.SYSTEM_ERROR).toBe('system:error');
        });

        it('应该包含应用事件', () => {
            expect(EventTypes.APP_REGISTER).toBe('app:register');
            expect(EventTypes.APP_UNREGISTER).toBe('app:unregister');
            expect(EventTypes.APP_STATUS_CHANGE).toBe('app:status:change');
            expect(EventTypes.APP_LOAD_START).toBe('app:load:start');
            expect(EventTypes.APP_LOAD_COMPLETE).toBe('app:load:complete');
            expect(EventTypes.APP_LOAD_FAILED).toBe('app:load:failed');
        });

        it('应该包含路由事件', () => {
            expect(EventTypes.ROUTE_CHANGE_START).toBe('route:change:start');
            expect(EventTypes.ROUTE_CHANGE_COMPLETE).toBe('route:change:complete');
            expect(EventTypes.ROUTE_CHANGE_FAILED).toBe('route:change:failed');
        });

        it('应该包含通信事件', () => {
            expect(EventTypes.MESSAGE_SEND).toBe('message:send');
            expect(EventTypes.MESSAGE_RECEIVE).toBe('message:receive');
            expect(EventTypes.COMMUNICATION_ERROR).toBe('communication:error');
        });
    });

    describe('HTTP_STATUS', () => {
        it('应该包含成功状态码', () => {
            expect(HTTP_STATUS.OK).toBe(200);
            expect(HTTP_STATUS.CREATED).toBe(201);
            expect(HTTP_STATUS.ACCEPTED).toBe(202);
            expect(HTTP_STATUS.NO_CONTENT).toBe(204);
        });

        it('应该包含客户端错误状态码', () => {
            expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
            expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
            expect(HTTP_STATUS.FORBIDDEN).toBe(403);
            expect(HTTP_STATUS.NOT_FOUND).toBe(404);
        });

        it('应该包含服务器错误状态码', () => {
            expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
            expect(HTTP_STATUS.NOT_IMPLEMENTED).toBe(501);
            expect(HTTP_STATUS.BAD_GATEWAY).toBe(502);
            expect(HTTP_STATUS.SERVICE_UNAVAILABLE).toBe(503);
        });
    });

    describe('CONFIG', () => {
        it('应该包含超时配置', () => {
            expect(CONFIG.TIMEOUT.DEFAULT).toBe(30000);
            expect(CONFIG.TIMEOUT.SHORT).toBe(5000);
            expect(CONFIG.TIMEOUT.LONG).toBe(60000);
            expect(CONFIG.TIMEOUT.RESOURCE_LOAD).toBe(10000);
            expect(CONFIG.TIMEOUT.APP_BOOTSTRAP).toBe(15000);
            expect(CONFIG.TIMEOUT.APP_MOUNT).toBe(10000);
        });

        it('应该包含重试配置', () => {
            expect(CONFIG.RETRY.MAX_ATTEMPTS).toBe(3);
            expect(CONFIG.RETRY.RESOURCE_LOAD).toBe(2);
            expect(CONFIG.RETRY.NETWORK_REQUEST).toBe(3);
            expect(CONFIG.RETRY.DELAY_BASE).toBe(1000);
            expect(CONFIG.RETRY.MAX_DELAY).toBe(10000);
        });

        it('应该包含缓存配置', () => {
            expect(CONFIG.CACHE.DEFAULT_SIZE).toBe(100);
            expect(CONFIG.CACHE.MAX_SIZE).toBe(1000);
            expect(CONFIG.CACHE.DEFAULT_TTL).toBe(3600000);
            expect(CONFIG.CACHE.SHORT_TTL).toBe(300000);
            expect(CONFIG.CACHE.LONG_TTL).toBe(86400000);
        });

        it('应该包含性能配置', () => {
            expect(CONFIG.PERFORMANCE.MEMORY_WARNING_THRESHOLD).toBe(80);
            expect(CONFIG.PERFORMANCE.MEMORY_CRITICAL_THRESHOLD).toBe(90);
            expect(CONFIG.PERFORMANCE.MONITOR_INTERVAL).toBe(5000);
            expect(CONFIG.PERFORMANCE.MAX_CONCURRENT_REQUESTS).toBe(10);
        });

        it('应该包含日志配置', () => {
            expect(CONFIG.LOG.MAX_ENTRIES).toBe(1000);
            expect(CONFIG.LOG.CLEANUP_INTERVAL).toBe(600000);
            expect(CONFIG.LOG.COLORS.DEBUG).toBe('#6B7280');
            expect(CONFIG.LOG.COLORS.INFO).toBe('#3B82F6');
            expect(CONFIG.LOG.COLORS.WARN).toBe('#F59E0B');
            expect(CONFIG.LOG.COLORS.ERROR).toBe('#EF4444');
        });
    });

    describe('REGEX', () => {
        it('应该包含URL验证正则', () => {
            expect(REGEX.URL.test('https://example.com')).toBe(true);
            expect(REGEX.URL.test('http://localhost:3000')).toBe(true);
            expect(REGEX.URL.test('invalid-url')).toBe(false);
        });

        it('应该包含邮箱验证正则', () => {
            expect(REGEX.EMAIL.test('<EMAIL>')).toBe(true);
            expect(REGEX.EMAIL.test('invalid-email')).toBe(false);
        });

        it('应该包含手机号验证正则', () => {
            expect(REGEX.PHONE_CN.test('13812345678')).toBe(true);
            expect(REGEX.PHONE_CN.test('12345678901')).toBe(false);
        });

        it('应该包含IPv4验证正则', () => {
            expect(REGEX.IPV4.test('***********')).toBe(true);
            expect(REGEX.IPV4.test('256.256.256.256')).toBe(false);
        });

        it('应该包含版本号验证正则', () => {
            expect(REGEX.SEMVER.test('1.0.0')).toBe(true);
            expect(REGEX.SEMVER.test('1.0.0-alpha.1')).toBe(true);
            expect(REGEX.SEMVER.test('invalid-version')).toBe(false);
        });

        it('应该包含应用名称验证正则', () => {
            expect(REGEX.APP_NAME.test('my-app')).toBe(true);
            expect(REGEX.APP_NAME.test('my_app')).toBe(true);
            expect(REGEX.APP_NAME.test('123-app')).toBe(false);
        });
    });

    describe('MIME_TYPES', () => {
        it('应该包含文本类型', () => {
            expect(MIME_TYPES.TEXT_PLAIN).toBe('text/plain');
            expect(MIME_TYPES.TEXT_HTML).toBe('text/html');
            expect(MIME_TYPES.TEXT_CSS).toBe('text/css');
            expect(MIME_TYPES.TEXT_JAVASCRIPT).toBe('text/javascript');
        });

        it('应该包含应用类型', () => {
            expect(MIME_TYPES.APPLICATION_JSON).toBe('application/json');
            expect(MIME_TYPES.APPLICATION_XML).toBe('application/xml');
            expect(MIME_TYPES.APPLICATION_PDF).toBe('application/pdf');
        });

        it('应该包含图片类型', () => {
            expect(MIME_TYPES.IMAGE_JPEG).toBe('image/jpeg');
            expect(MIME_TYPES.IMAGE_PNG).toBe('image/png');
            expect(MIME_TYPES.IMAGE_GIF).toBe('image/gif');
            expect(MIME_TYPES.IMAGE_SVG).toBe('image/svg+xml');
        });
    });

    describe('BROWSER_FEATURES', () => {
        it('应该包含支持的特性列表', () => {
            expect(BROWSER_FEATURES.STORAGE).toContain('localStorage');
            expect(BROWSER_FEATURES.STORAGE).toContain('sessionStorage');
            expect(BROWSER_FEATURES.STORAGE).toContain('indexedDB');

            expect(BROWSER_FEATURES.NETWORK).toContain('fetch');
            expect(BROWSER_FEATURES.NETWORK).toContain('XMLHttpRequest');
            expect(BROWSER_FEATURES.NETWORK).toContain('WebSocket');

            expect(BROWSER_FEATURES.WORKERS).toContain('Worker');
            expect(BROWSER_FEATURES.WORKERS).toContain('ServiceWorker');
            expect(BROWSER_FEATURES.WORKERS).toContain('SharedWorker');
        });
    });

    describe('DEFAULT_APP_CONFIG', () => {
        it('应该包含默认应用配置', () => {
            expect(DEFAULT_APP_CONFIG.name).toBe('');
            expect(DEFAULT_APP_CONFIG.entry).toBe('');
            expect(DEFAULT_APP_CONFIG.container).toBe('');
            expect(DEFAULT_APP_CONFIG.activeRule).toBe('');
            expect(DEFAULT_APP_CONFIG.props).toEqual({});
            expect(DEFAULT_APP_CONFIG.loader.timeout).toBe(CONFIG.TIMEOUT.RESOURCE_LOAD);
            expect(DEFAULT_APP_CONFIG.loader.retries).toBe(CONFIG.RETRY.RESOURCE_LOAD);
            expect(DEFAULT_APP_CONFIG.loader.cache).toBe(true);
        });
    });

    describe('存储和环境常量', () => {
        it('应该包含环境变量键名', () => {
            expect(ENV_KEYS.NODE_ENV).toBe('NODE_ENV');
            expect(ENV_KEYS.DEBUG).toBe('DEBUG');
            expect(ENV_KEYS.API_BASE_URL).toBe('API_BASE_URL');
        });

        it('应该包含存储键名', () => {
            expect(STORAGE_KEYS.APP_CONFIG).toBe('micro-core:app-config');
            expect(STORAGE_KEYS.USER_SETTINGS).toBe('micro-core:user-settings');
            expect(STORAGE_KEYS.CACHE_DATA).toBe('micro-core:cache');
        });

        it('应该包含CSS类名前缀', () => {
            expect(CSS_PREFIXES.MICRO_APP).toBe('micro-app');
            expect(CSS_PREFIXES.LOADING).toBe('micro-loading');
            expect(CSS_PREFIXES.ERROR).toBe('micro-error');
        });

        it('应该包含数据属性名', () => {
            expect(DATA_ATTRIBUTES.APP_NAME).toBe('data-micro-app');
            expect(DATA_ATTRIBUTES.APP_STATUS).toBe('data-micro-status');
            expect(DATA_ATTRIBUTES.APP_VERSION).toBe('data-micro-version');
        });

        it('应该包含元数据键名', () => {
            expect(META_KEYS.APP_META).toBe('__MICRO_APP_META__');
            expect(META_KEYS.GLOBAL_CONFIG).toBe('__MICRO_GLOBAL_CONFIG__');
            expect(META_KEYS.SHARED_DATA).toBe('__MICRO_SHARED_DATA__');
        });
    });

    describe('constants 对象', () => {
        it('应该包含所有常量', () => {
            expect(constants.AppStatus).toBe(AppStatus);
            expect(constants.ErrorCodes).toBe(ErrorCodes);
            expect(constants.EventTypes).toBe(EventTypes);
            expect(constants.HTTP_STATUS).toBe(HTTP_STATUS);
            expect(constants.CONFIG).toBe(CONFIG);
            expect(constants.REGEX).toBe(REGEX);
            expect(constants.MIME_TYPES).toBe(MIME_TYPES);
        });
    });

    describe('向后兼容性', () => {
        it('应该保持向后兼容的导出', () => {
            expect(SharedErrorCodes).toBe(ErrorCodes);
            expect(SharedEventTypes).toBe(EventTypes);
            expect(SHARED_CONFIG).toBe(CONFIG);
        });
    });
});
