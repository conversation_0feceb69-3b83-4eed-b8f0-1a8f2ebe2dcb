/**
 * @fileoverview 错误相关类型定义
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import type { ErrorCodeType } from '../constants';

/**
 * 错误上下文
 */
export interface ErrorContext {
    /** 操作名称 */
    operation?: string;
    /** 应用名称 */
    appName?: string;
    /** 插件名称 */
    pluginName?: string;
    /** 额外信息 */
    [key: string]: any;
}

/**
 * 错误报告
 */
export interface ErrorReport {
    /** 错误代码 */
    code: ErrorCodeType;
    /** 错误消息 */
    message: string;
    /** 错误上下文 */
    context: ErrorContext;
    /** 错误堆栈 */
    stack?: string;
    /** 时间戳 */
    timestamp: number;
    /** 用户代理 */
    userAgent?: string;
    /** 页面URL */
    url?: string;
}

/**
 * 错误处理器函数
 */
export type ErrorHandlerFn = (error: Error) => void;

/**
 * 错误恢复策略
 */
export interface ErrorRecoveryStrategy {
    /** 策略名称 */
    name: string;
    /** 是否可以恢复 */
    canRecover: (error: Error) => boolean;
    /** 恢复函数 */
    recover: (error: Error) => Promise<void>;
}