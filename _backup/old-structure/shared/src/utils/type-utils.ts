/**
 * @fileoverview 类型工具函数
 * @description 提供类型检查、转换和验证的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 检查值是否为函数
 */
export function isFunction(value: unknown): value is Function {
    return typeof value === 'function';
}

/**
 * 检查值是否为对象
 */
export function isObject(value: unknown): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 检查值是否为数组
 */
export function isArray(value: unknown): value is Array<any> {
    return Array.isArray(value);
}

/**
 * 检查值是否为字符串
 */
export function isString(value: unknown): value is string {
    return typeof value === 'string';
}

/**
 * 检查值是否为数字
 */
export function isNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查值是否为布尔值
 */
export function isBoolean(value: unknown): value is boolean {
    return typeof value === 'boolean';
}

/**
 * 检查值是否为 Promise
 */
export function isPromise(value: unknown): value is Promise<any> {
    return value instanceof Promise || (
        isObject(value) &&
        isFunction((value as any).then) &&
        isFunction((value as any).catch)
    );
}

/**
 * 检查值是否为空值（null 或 undefined）
 */
export function isNullish(value: unknown): value is null | undefined {
    return value === null || value === undefined;
}

/**
 * 检查值是否为空（null、undefined 或空字符串）
 */
export function isEmpty(value: unknown): boolean {
    if (isNullish(value)) return true;
    if (isString(value)) return value.trim() === '';
    if (isArray(value)) return value.length === 0;
    if (isObject(value)) return Object.keys(value).length === 0;
    return false;
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as unknown as T;
    }

    if (obj instanceof Array) {
        return obj.map(item => deepClone(item)) as unknown as T;
    }

    if (typeof obj === 'object') {
        const cloned = {} as T;
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
            }
        }
        return cloned;
    }

    return obj;
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T = any>(json: string, defaultValue?: T): T | null {
    try {
        return JSON.parse(json);
    } catch {
        return defaultValue ?? null;
    }
}

/**
 * 安全的 JSON 字符串化
 */
export function safeJsonStringify(obj: any, space?: number): string {
    try {
        return JSON.stringify(obj, null, space);
    } catch {
        return '{}';
    }
}

/**
 * 类型断言工具
 */
export function assertType<T>(value: unknown, predicate: (value: unknown) => value is T, message?: string): asserts value is T {
    if (!predicate(value)) {
        throw new Error(message || `类型断言失败`);
    }
}

/**
 * 获取值的类型字符串
 */
export function getType(value: unknown): string {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (Array.isArray(value)) return 'array';
    if (value instanceof Date) return 'date';
    if (value instanceof RegExp) return 'regexp';
    return typeof value;
}