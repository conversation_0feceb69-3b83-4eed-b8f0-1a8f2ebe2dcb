/**
 * @fileoverview Shared Utilities for Micro-Core Builders
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

import type {
  BaseBuilderConfig,
  BaseBuilderOptions,
  BuildResult,
  ValidationResult
} from './types';

/**
 * 配置验证工具
 */
export class ConfigValidator {
  /**
   * 验证构建器配置
   */
  static validateConfig(config: BaseBuilderConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必需字段验证
    if (!config.id) {
      errors.push('Config id is required');
    }
    if (!config.name) {
      errors.push('Config name is required');
    }
    if (!config.entry) {
      errors.push('Config entry is required');
    }

    // 格式验证
    if (config.id && !/^[a-zA-Z0-9-_]+$/.test(config.id)) {
      errors.push('Config id must contain only alphanumeric characters, hyphens, and underscores');
    }

    if (config.publicPath && !config.publicPath.startsWith('/')) {
      warnings.push('Public path should start with "/"');
    }

    // 路径验证
    if (config.outDir && config.outDir.includes('..')) {
      errors.push('Output directory cannot contain ".." path segments');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证构建器选项
   */
  static validateOptions(options: BaseBuilderOptions): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 微应用配置验证
    if (options.microApp) {
      if (!options.microApp.name) {
        errors.push('MicroApp name is required');
      }
      if (!options.microApp.entry) {
        errors.push('MicroApp entry is required');
      }
    }

    // 插件配置验证
    if (options.plugins) {
      options.plugins.forEach((plugin, index) => {
        if (!plugin.name) {
          errors.push(`Plugin at index ${index} must have a name`);
        }
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

/**
 * 路径工具
 */
export class PathUtils {
  /**
   * 规范化路径
   */
  static normalizePath(path: string): string {
    return path.replace(/\\/g, '/').replace(/\/+/g, '/');
  }

  /**
   * 解析相对路径
   */
  static resolvePath(basePath: string, relativePath: string): string {
    if (relativePath.startsWith('/')) {
      return relativePath;
    }
    return this.normalizePath(`${basePath}/${relativePath}`);
  }

  /**
   * 获取文件扩展名
   */
  static getExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.slice(lastDot + 1);
  }

  /**
   * 获取文件名（不含扩展名）
   */
  static getBasename(filename: string): string {
    const lastSlash = filename.lastIndexOf('/');
    const name = lastSlash === -1 ? filename : filename.slice(lastSlash + 1);
    const lastDot = name.lastIndexOf('.');
    return lastDot === -1 ? name : name.slice(0, lastDot);
  }
}

/**
 * 配置合并工具
 */
export class ConfigMerger {
  /**
   * 深度合并对象 - 支持多个源对象
   */
  static deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
    if (!target || typeof target !== 'object') {
      throw new Error('Target must be a valid object');
    }

    let result = { ...target };

    for (const source of sources) {
      if (!source || typeof source !== 'object') {
        continue; // Skip invalid sources
      }

      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          const sourceValue = source[key];
          const targetValue = result[key];

          if (Array.isArray(sourceValue)) {
            // Handle arrays - merge or replace based on configuration
            result[key] = Array.isArray(targetValue)
              ? this.mergeArrays(targetValue, sourceValue) as any
              : [...sourceValue] as any;
          } else if (this.isObject(sourceValue) && this.isObject(targetValue)) {
            result[key] = this.deepMerge(targetValue as Record<string, any>, sourceValue as Record<string, any>) as any;
          } else if (sourceValue !== undefined) {
            result[key] = sourceValue as any;
          }
        }
      }
    }

    return result;
  }

  /**
   * 判断是否为对象
   */
  private static isObject(value: any): value is Record<string, any> {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * 合并数组（去重）
   */
  static mergeArrays<T>(arr1: T[], arr2: T[]): T[] {
    if (!Array.isArray(arr1) || !Array.isArray(arr2)) {
      throw new Error('Both arguments must be arrays');
    }
    return [...new Set([...arr1, ...arr2])];
  }
}

/**
 * 错误处理工具
 */
export class ErrorHandler {
  /**
   * 格式化错误信息
   */
  static formatError(error: Error, context?: string): {
    message: string;
    stack?: string;
    context?: string;
  } {
    return {
      message: error.message,
      stack: error.stack,
      context
    };
  }

  /**
   * 创建构建错误
   */
  static createBuildError(message: string, file?: string, line?: number): NonNullable<BuildResult['errors']>[0] {
    return {
      message,
      file,
      line
    };
  }

  /**
   * 安全执行异步函数
   */
  static async safeExecute<T>(
    fn: () => Promise<T>,
    fallback?: T,
    onError?: (error: Error) => void
  ): Promise<T | undefined> {
    if (typeof fn !== 'function') {
      throw new Error('First argument must be a function');
    }

    try {
      return await fn();
    } catch (error) {
      const formattedError = error instanceof Error ? error : new Error(String(error));

      if (onError) {
        try {
          onError(formattedError);
        } catch (handlerError) {
          console.error('Error in error handler:', handlerError);
        }
      }

      return fallback;
    }
  }
}

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private static timers = new Map<string, number>();

  /**
   * 开始计时
   */
  static startTimer(name: string): void {
    this.timers.set(name, Date.now());
  }

  /**
   * 结束计时并返回耗时
   */
  static endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      throw new Error(`Timer "${name}" not found`);
    }

    const duration = Date.now() - startTime;
    this.timers.delete(name);
    return duration;
  }

  /**
   * 格式化文件大小
   */
  static formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 计算文件大小
   */
  static calculateSize(content: string | Uint8Array): number {
    if (typeof content === 'string') {
      return new TextEncoder().encode(content).length;
    }
    return content.length;
  }
}

/**
 * 缓存工具
 */
export class CacheManager {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  /**
   * 设置缓存
   */
  static set(key: string, data: any, ttl: number = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 获取缓存
   */
  static get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) {
      return null;
    }

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  /**
   * 清除缓存
   */
  static clear(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 清理过期缓存
   */
  static cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 使用shared/utils的Logger
export { createLogger as Logger } from '@micro-core/shared/utils';
