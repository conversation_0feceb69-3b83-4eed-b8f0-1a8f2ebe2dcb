/**
 * @fileoverview Base Builder Class for Micro-Core Builders
 * <AUTHOR> <<EMAIL>>
 * @copyright 2025 Micro-Core. All rights reserved.
 */

// Note: MicroCoreKernel type will be available when @micro-core/core is implemented
export interface MicroCoreKernel {
  registerBuilder?(name: string, builder: any): void;
  unregisterBuilder?(name: string): void;
}
import type {
  BaseBuilderConfig,
  BaseBuilderOptions,
  Builder,
  BuilderEvent,
  BuilderEventListener,
  BuildResult,
  DevServerConfig
} from './types';
import {
  ConfigValidator,
  ErrorHandler,
  PerformanceMonitor
} from './utils';

// 临时使用简单的logger实现，直到shared/utils包可用
const createLogger = (namespace: string) => ({
  debug: (msg: string, ...args: any[]) => console.debug(`[${namespace}] ${msg}`, ...args),
  info: (msg: string, ...args: any[]) => console.info(`[${namespace}] ${msg}`, ...args),
  warn: (msg: string, ...args: any[]) => console.warn(`[${namespace}] ${msg}`, ...args),
  error: (msg: string, ...args: any[]) => console.error(`[${namespace}] ${msg}`, ...args),
  log: (msg: string, ...args: any[]) => console.log(`[${namespace}] ${msg}`, ...args)
});

/**
 * 构建器基类
 * 提供所有构建器的通用功能和接口实现
 */
export abstract class BaseBuilder implements Builder {
  /** 构建器名称 */
  public abstract readonly name: string;

  /** 构建器版本 */
  public abstract readonly version: string;

  /** 内核实例 */
  protected kernel?: MicroCoreKernel;

  /** 构建器选项 */
  protected options: BaseBuilderOptions;

  /** 当前配置 */
  protected currentConfig?: BaseBuilderConfig;

  /** 是否正在运行 */
  protected isRunning = false;

  /** 开发服务器实例 */
  protected devServer?: any;

  /** 事件监听器 */
  private eventListeners = new Map<BuilderEvent, Set<BuilderEventListener>>();

  /** 文件监听器 */
  private fileWatcher?: any;

  /** 构建状态 */
  private buildStatus: 'idle' | 'building' | 'serving' | 'error' = 'idle';

  /** Logger实例 */
  protected logger = createLogger('BaseBuilder');

  constructor(options: BaseBuilderOptions = {}) {
    this.options = { ...options };
    this.validateOptions();
  }

  /**
   * 安装插件到内核
   */
  install(kernel: MicroCoreKernel): void {
    this.kernel = kernel;
    // 更新logger命名空间
    this.logger = createLogger(`Builder:${this.name}`);
    this.logger.info(`Installing ${this.name} builder to kernel`);

    // 注册构建器到内核
    kernel.registerBuilder?.(this.name, this);

    this.onInstall(kernel);
  }

  /**
   * 从内核卸载插件
   */
  uninstall(kernel: MicroCoreKernel): void {
    this.logger.info(`Uninstalling ${this.name} builder from kernel`);

    // 停止开发服务器
    if (this.isRunning && this.devServer) {
      this.stop().catch(error => {
        this.logger.error(`Error stopping dev server during uninstall: ${error.message}`);
      });
    }

    // 从内核注销构建器
    kernel.unregisterBuilder?.(this.name);

    this.onUninstall(kernel);
    this.kernel = undefined;
  }

  /**
   * 创建构建配置
   */
  createConfig(config: BaseBuilderConfig): any {
    // 验证配置
    const validation = ConfigValidator.validateConfig(config);
    if (!validation.valid) {
      throw new Error(`Invalid config: ${validation.errors.join(', ')}`);
    }

    // 输出警告
    validation.warnings.forEach(warning => {
      this.logger.warn(warning);
    });

    this.currentConfig = config;
    this.logger.debug(`Creating config for ${config.name}`, config);

    return this.createBuilderConfig(config);
  }

  /**
   * 执行构建
   */
  async build(config: BaseBuilderConfig): Promise<BuildResult> {
    const timerId = `build-${config.id}`;
    PerformanceMonitor.startTimer(timerId);

    // Set build status
    this.buildStatus = 'building';

    try {
      this.logger.info(`Starting build for ${config.name}`);
      this.emit('build:start', { config });

      const builderConfig = this.createConfig(config);
      const result = await this.executeBuild(builderConfig);

      const duration = PerformanceMonitor.endTimer(timerId);

      // 添加性能统计
      if (result.stats) {
        result.stats.duration = duration;
      } else {
        result.stats = {
          duration,
          fileCount: result.outputs?.length || 0,
          totalSize: result.outputs?.reduce((sum, output) => sum + output.size, 0) || 0,
          errors: result.errors?.length || 0,
          warnings: result.warnings?.length || 0
        };
      }

      this.logger.info(`Build completed for ${config.name} in ${duration}ms`);
      this.buildStatus = 'idle';
      this.emit('build:end', { config, result });

      return result;
    } catch (error) {
      PerformanceMonitor.endTimer(timerId);
      this.buildStatus = 'error';
      const formattedError = ErrorHandler.formatError(error as Error, `build-${config.id}`);

      this.logger.error(`Build failed for ${config.name}: ${formattedError.message}`);
      this.emit('build:error', { config, error: formattedError });

      return {
        success: false,
        errors: [ErrorHandler.createBuildError(formattedError.message)],
        stats: {
          duration: 0,
          fileCount: 0,
          totalSize: 0,
          errors: 1,
          warnings: 0
        }
      };
    }
  }

  /**
   * 启动开发服务器
   */
  async serve(config: BaseBuilderConfig & { devServer?: DevServerConfig }): Promise<any> {
    if (this.isRunning) {
      this.logger.warn(`Dev server for ${config.name} is already running`);
      return this.devServer;
    }

    try {
      this.logger.info(`Starting dev server for ${config.name}`);
      this.buildStatus = 'serving';
      this.emit('serve:start', { config });

      const builderConfig = this.createConfig(config);
      this.devServer = await this.startDevServer(builderConfig, config.devServer);
      this.isRunning = true;

      // Start file watching if enabled
      if (config.devServer?.hot !== false) {
        this.startFileWatching(config);
      }

      this.logger.info(`Dev server started for ${config.name}`);
      return this.devServer;
    } catch (error) {
      this.buildStatus = 'error';
      const formattedError = ErrorHandler.formatError(error as Error, `serve-${config.id}`);
      this.logger.error(`Failed to start dev server for ${config.name}: ${formattedError.message}`);
      this.emit('serve:error', { config, error: formattedError });
      throw error;
    }
  }

  /**
   * 停止开发服务器
   */
  async stop(): Promise<void> {
    if (!this.isRunning || !this.devServer) {
      Logger.warn(`Dev server is not running`);
      return;
    }

    try {
      Logger.info(`Stopping dev server`);

      // Stop file watching
      this.stopFileWatching();

      await this.stopDevServer(this.devServer);
      this.devServer = undefined;
      this.isRunning = false;
      this.buildStatus = 'idle';

      Logger.info(`Dev server stopped`);
      this.emit('serve:stop', {});
    } catch (error) {
      this.buildStatus = 'error';
      const formattedError = ErrorHandler.formatError(error as Error, 'stop-server');
      Logger.error(`Failed to stop dev server: ${formattedError.message}`);
      throw error;
    }
  }

  /**
   * 获取构建器状态
   */
  getStatus() {
    return {
      name: this.name,
      version: this.version,
      isRunning: this.isRunning,
      buildStatus: this.buildStatus,
      config: this.currentConfig,
      hasFileWatcher: !!this.fileWatcher,
      eventListenerCount: Array.from(this.eventListeners.values())
        .reduce((total, listeners) => total + listeners.size, 0)
    };
  }

  /**
   * 添加事件监听器
   */
  on(event: BuilderEvent, listener: BuilderEventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }

  /**
   * 移除事件监听器
   */
  off(event: BuilderEvent, listener: BuilderEventListener): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * 触发事件
   */
  protected emit(event: BuilderEvent, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const eventData = {
        type: event,
        data,
        timestamp: Date.now()
      };

      listeners.forEach(listener => {
        try {
          listener(eventData);
        } catch (error) {
          Logger.error(`Error in event listener for ${event}: ${(error as Error).message}`);
        }
      });
    }
  }

  /**
   * 验证选项
   */
  protected validateOptions(): void {
    const validation = ConfigValidator.validateOptions(this.options);
    if (!validation.valid) {
      throw new Error(`Invalid options: ${validation.errors.join(', ')}`);
    }

    validation.warnings.forEach(warning => {
      Logger.warn(warning);
    });
  }

  // 抽象方法 - 子类必须实现

  /**
   * 创建特定构建器的配置
   */
  protected abstract createBuilderConfig(config: BaseBuilderConfig): any;

  /**
   * 执行构建
   */
  protected abstract executeBuild(builderConfig: any): Promise<BuildResult>;

  /**
   * 启动开发服务器
   */
  protected abstract startDevServer(builderConfig: any, devServerConfig?: DevServerConfig): Promise<any>;

  /**
   * 停止开发服务器
   */
  protected abstract stopDevServer(server: any): Promise<void>;

  // 可选的生命周期钩子

  /**
   * 安装时调用
   */
  protected onInstall(kernel: MicroCoreKernel): void {
    // 子类可以重写此方法
  }

  /**
   * 卸载时调用
   */
  protected onUninstall(kernel: MicroCoreKernel): void {
    // 子类可以重写此方法
  }

  /**
   * 配置变更时调用
   */
  protected onConfigChange(config: BaseBuilderConfig): void {
    this.emit('config:change', { config });
  }

  /**
   * 文件变更时调用
   */
  protected onFileChange(file: string): void {
    this.emit('file:change', { file });
  }

  /**
   * 启动文件监听
   */
  protected startFileWatching(config: BaseBuilderConfig): void {
    if (this.fileWatcher) {
      this.stopFileWatching();
    }

    try {
      // 简单的文件监听实现 - 实际项目中应使用 chokidar 等库
      Logger.debug(`Starting file watching for ${config.entry}`);

      // 模拟文件监听器
      this.fileWatcher = {
        close: () => {
          Logger.debug('File watcher closed');
        }
      };

      Logger.info('File watching started');
    } catch (error) {
      Logger.error('Failed to start file watching:', error);
    }
  }

  /**
   * 停止文件监听
   */
  protected stopFileWatching(): void {
    if (this.fileWatcher) {
      try {
        this.fileWatcher.close();
        this.fileWatcher = undefined;
        Logger.debug('File watching stopped');
      } catch (error) {
        Logger.error('Failed to stop file watching:', error);
      }
    }
  }
}
