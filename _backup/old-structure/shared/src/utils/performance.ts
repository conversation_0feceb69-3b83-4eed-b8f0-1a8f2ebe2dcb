/**
 * @fileoverview 性能工具函数
 * @description 提供性能监控、测量和优化相关的工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

/**
 * 性能指标接口
 */
export interface PerformanceMetric {
    name: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    memory?: {
        used: number;
        total: number;
        limit: number;
    };
    metadata?: Record<string, any>;
}

/**
 * 性能测量器
 */
export class PerformanceMeasurer {
    private metrics = new Map<string, PerformanceMetric>();
    private timers = new Map<string, number>();

    /**
     * 开始测量
     */
    start(name: string, metadata?: Record<string, any>): void {
        const startTime = performance.now();
        this.timers.set(name, startTime);

        const metric: PerformanceMetric = {
            name,
            startTime,
            metadata
        };

        // 记录内存使用情况（如果可用）
        if ('memory' in performance) {
            const memory = (performance as any).memory;
            metric.memory = {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit
            };
        }

        this.metrics.set(name, metric);
    }

    /**
     * 结束测量
     */
    end(name: string): PerformanceMetric | null {
        const startTime = this.timers.get(name);
        if (!startTime) {
            console.warn(`性能测量 "${name}" 未找到开始时间`);
            return null;
        }

        const endTime = performance.now();
        const duration = endTime - startTime;

        const metric = this.metrics.get(name);
        if (metric) {
            metric.endTime = endTime;
            metric.duration = duration;

            // 更新内存使用情况
            if ('memory' in performance && metric.memory) {
                const memory = (performance as any).memory;
                metric.memory = {
                    ...metric.memory,
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                };
            }
        }

        this.timers.delete(name);
        return metric;
    }

    /**
     * 获取指标
     */
    getMetric(name: string): PerformanceMetric | undefined {
        return this.metrics.get(name);
    }

    /**
     * 获取所有指标
     */
    getAllMetrics(): PerformanceMetric[] {
        return Array.from(this.metrics.values());
    }

    /**
     * 清空指标
     */
    clear(): void {
        this.metrics.clear();
        this.timers.clear();
    }

    /**
     * 生成报告
     */
    generateReport(): string {
        const metrics = this.getAllMetrics();
        let report = '# 性能报告\n\n';

        if (metrics.length === 0) {
            report += '暂无性能数据\n';
            return report;
        }

        report += '| 指标名称 | 耗时(ms) | 内存使用(MB) | 状态 |\n';
        report += '|---------|----------|-------------|------|\n';

        metrics.forEach(metric => {
            const duration = metric.duration ? metric.duration.toFixed(2) : '进行中';
            const memory = metric.memory ? (metric.memory.used / 1024 / 1024).toFixed(2) : 'N/A';
            const status = metric.duration ? '完成' : '进行中';

            report += `| ${metric.name} | ${duration} | ${memory} | ${status} |\n`;
        });

        return report;
    }
}

/**
 * 全局性能测量器实例
 */
export const globalPerformanceMeasurer = new PerformanceMeasurer();

/**
 * 简化的性能测量函数
 */
export function measurePerformance<T>(
    name: string,
    fn: () => T | Promise<T>,
    metadata?: Record<string, any>
): T | Promise<T> {
    globalPerformanceMeasurer.start(name, metadata);

    const result = fn();

    if (result instanceof Promise) {
        return result.finally(() => {
            globalPerformanceMeasurer.end(name);
        });
    } else {
        globalPerformanceMeasurer.end(name);
        return result;
    }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate = false
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return function executedFunction(...args: Parameters<T>) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };

        const callNow = immediate && !timeout;

        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(later, wait);

        if (callNow) func(...args);
    };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle = false;

    return function executedFunction(...args: Parameters<T>) {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 */
export async function retry<T>(
    fn: () => Promise<T>,
    maxAttempts = 3,
    delayMs = 1000
): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));

            if (attempt === maxAttempts) {
                throw lastError;
            }

            await delay(delayMs * attempt); // 指数退避
        }
    }

    throw lastError!;
}

/**
 * 缓存装饰器
 */
export function memoize<T extends (...args: any[]) => any>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string
): T {
    const cache = new Map<string, ReturnType<T>>();

    return ((...args: Parameters<T>): ReturnType<T> => {
        const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);

        if (cache.has(key)) {
            return cache.get(key)!;
        }

        const result = fn(...args);
        cache.set(key, result);
        return result;
    }) as T;
}

/**
 * 批处理函数
 */
export function batch<T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    batchSize = 10,
    delayMs = 0
): Promise<R[]> {
    return new Promise(async (resolve, reject) => {
        const results: R[] = [];
        const batches: T[][] = [];

        // 分批
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }

        try {
            for (const batch of batches) {
                const batchResults = await processor(batch);
                results.push(...batchResults);

                if (delayMs > 0) {
                    await delay(delayMs);
                }
            }

            resolve(results);
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * 资源加载性能监控
 */
export function monitorResourceLoading(): void {
    if (typeof window === 'undefined') return;

    const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
            if (entry.entryType === 'resource') {
                const resourceEntry = entry as PerformanceResourceTiming;
                console.log(`资源加载: ${resourceEntry.name}`, {
                    duration: resourceEntry.duration,
                    size: resourceEntry.transferSize,
                    type: resourceEntry.initiatorType
                });
            }
        });
    });

    observer.observe({ entryTypes: ['resource'] });
}

/**
 * 长任务监控
 */
export function monitorLongTasks(): void {
    if (typeof window === 'undefined') return;

    const observer = new PerformanceObserver(list => {
        list.getEntries().forEach(entry => {
            console.warn(`检测到长任务:`, {
                duration: entry.duration,
                startTime: entry.startTime,
                name: entry.name
            });
        });
    });

    observer.observe({ entryTypes: ['longtask'] });
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): {
    used: number;
    total: number;
    limit: number;
} | null {
    if (typeof window === 'undefined' || !('memory' in performance)) {
        return null;
    }

    const memory = (performance as any).memory;
    return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
    };
}

/**
 * FPS 监控
 */
export class FPSMonitor {
    private fps = 0;
    private lastTime = 0;
    private frameCount = 0;
    private callbacks: ((fps: number) => void)[] = [];
    private animationId: number | null = null;

    start(): void {
        if (this.animationId) return;

        const tick = (currentTime: number) => {
            this.frameCount++;

            if (currentTime - this.lastTime >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
                this.frameCount = 0;
                this.lastTime = currentTime;

                this.callbacks.forEach(callback => callback(this.fps));
            }

            this.animationId = requestAnimationFrame(tick);
        };

        this.animationId = requestAnimationFrame(tick);
    }

    stop(): void {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    onUpdate(callback: (fps: number) => void): void {
        this.callbacks.push(callback);
    }

    getFPS(): number {
        return this.fps;
    }
}

/**
 * 创建 FPS 监控器
 */
export function createFPSMonitor(): FPSMonitor {
    return new FPSMonitor();
}