/**
 * @fileoverview 验证工具函数
 * @description 提供各种数据验证和校验功能
 * <AUTHOR> <<EMAIL>>
 * @version 2.0.0
 */

import { isArray, isFunction, isNumber, isObject, isString } from './type-utils';

/**
 * 验证规则接口
 */
export interface ValidationRule {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: any) => boolean | string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    valid: boolean;
    errors: string[];
}

/**
 * 验证单个值
 */
export function validateValue(value: any, rules: ValidationRule): ValidationResult {
    const errors: string[] = [];

    // 必填验证
    if (rules.required && (value === null || value === undefined || value === '')) {
        errors.push('此字段为必填项');
        return { valid: false, errors };
    }

    // 如果值为空且非必填，跳过其他验证
    if (!rules.required && (value === null || value === undefined || value === '')) {
        return { valid: true, errors: [] };
    }

    // 类型验证
    if (rules.type) {
        switch (rules.type) {
            case 'string':
                if (!isString(value)) errors.push('必须是字符串类型');
                break;
            case 'number':
                if (!isNumber(value)) errors.push('必须是数字类型');
                break;
            case 'boolean':
                if (typeof value !== 'boolean') errors.push('必须是布尔类型');
                break;
            case 'object':
                if (!isObject(value)) errors.push('必须是对象类型');
                break;
            case 'array':
                if (!isArray(value)) errors.push('必须是数组类型');
                break;
            case 'function':
                if (!isFunction(value)) errors.push('必须是函数类型');
                break;
        }
    }

    // 长度/大小验证
    if (rules.min !== undefined) {
        if (isString(value) && value.length < rules.min) {
            errors.push(`长度不能少于 ${rules.min} 个字符`);
        } else if (isNumber(value) && value < rules.min) {
            errors.push(`值不能小于 ${rules.min}`);
        } else if (isArray(value) && value.length < rules.min) {
            errors.push(`数组长度不能少于 ${rules.min}`);
        }
    }

    if (rules.max !== undefined) {
        if (isString(value) && value.length > rules.max) {
            errors.push(`长度不能超过 ${rules.max} 个字符`);
        } else if (isNumber(value) && value > rules.max) {
            errors.push(`值不能大于 ${rules.max}`);
        } else if (isArray(value) && value.length > rules.max) {
            errors.push(`数组长度不能超过 ${rules.max}`);
        }
    }

    // 正则表达式验证
    if (rules.pattern && isString(value) && !rules.pattern.test(value)) {
        errors.push('格式不正确');
    }

    // 自定义验证
    if (rules.custom) {
        const customResult = rules.custom(value);
        if (customResult === false) {
            errors.push('自定义验证失败');
        } else if (isString(customResult)) {
            errors.push(customResult);
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 验证对象
 */
export function validateObject(
    obj: Record<string, any>,
    schema: Record<string, ValidationRule>
): ValidationResult {
    const errors: string[] = [];

    for (const [key, rules] of Object.entries(schema)) {
        const result = validateValue(obj[key], rules);
        if (!result.valid) {
            errors.push(...result.errors.map(error => `${key}: ${error}`));
        }
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * URL 验证
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 邮箱验证
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 手机号验证（中国大陆）
 */
export function isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

/**
 * 身份证号验证（中国大陆）
 */
export function isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCard);
}

/**
 * IP 地址验证
 */
export function isValidIp(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

/**
 * 端口号验证
 */
export function isValidPort(port: number | string): boolean {
    const portNum = typeof port === 'string' ? parseInt(port, 10) : port;
    return Number.isInteger(portNum) && portNum >= 1 && portNum <= 65535;
}

/**
 * 版本号验证（语义化版本）
 */
export function isValidVersion(version: string): boolean {
    const versionRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    return versionRegex.test(version);
}

/**
 * 应用名称验证
 */
export function isValidAppName(name: string): boolean {
    // 应用名称只能包含字母、数字、连字符和下划线，且不能以数字开头
    const nameRegex = /^[a-zA-Z_][a-zA-Z0-9_-]*$/;
    return nameRegex.test(name) && name.length >= 2 && name.length <= 50;
}

/**
 * 路由路径验证
 */
export function isValidRoutePath(path: string): boolean {
    // 路由路径必须以 / 开头，可以包含字母、数字、连字符、下划线和斜杠
    const pathRegex = /^\/[a-zA-Z0-9_/-]*$/;
    return pathRegex.test(path);
}

/**
 * 容器选择器验证
 */
export function isValidSelector(selector: string): boolean {
    try {
        document.querySelector(selector);
        return true;
    } catch {
        return false;
    }
}

/**
 * 批量验证工具
 */
export class Validator {
    private rules: Record<string, ValidationRule> = {};

    /**
     * 添加验证规则
     */
    addRule(field: string, rule: ValidationRule): this {
        this.rules[field] = rule;
        return this;
    }

    /**
     * 验证数据
     */
    validate(data: Record<string, any>): ValidationResult {
        return validateObject(data, this.rules);
    }

    /**
     * 清空规则
     */
    clear(): this {
        this.rules = {};
        return this;
    }
}

/**
 * 创建验证器实例
 */
export function createValidator(): Validator {
    return new Validator();
}