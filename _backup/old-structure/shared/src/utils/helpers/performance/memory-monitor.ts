/**
 * @fileoverview 内存监控器
 * @description 提供内存使用情况监控和警告功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '../../../utils/src/logger';

const logger = createLogger('micro-core:memory');

/**
 * 内存信息接口
 */
export interface MemoryInfo {
    used: number;
    total: number;
    percentage: number;
    warning: boolean;
}

/**
 * 获取内存信息
 */
export function getMemoryInfo(): MemoryInfo | null {
    if (typeof performance === 'undefined' || !('memory' in performance)) {
        return null;
    }

    const memory = (performance as any).memory;
    const used = memory.usedJSHeapSize;
    const total = memory.totalJSHeapSize;
    const percentage = (used / total) * 100;

    return {
        used,
        total,
        percentage,
        warning: percentage > 80 // 内存使用超过80%时警告
    };
}

/**
 * 创建内存监控器
 */
export function createMemoryMonitor(options: {
    interval?: number;
    threshold?: number;
    onWarning?: (info: MemoryInfo) => void;
    onCritical?: (info: MemoryInfo) => void;
} = {}) {
    const {
        interval = 5000,
        threshold = 80,
        onWarning,
        onCritical
    } = options;

    let intervalId: NodeJS.Timeout | null = null;
    let lastWarningTime = 0;

    return {
        start() {
            if (intervalId) return;

            intervalId = setInterval(() => {
                const memInfo = getMemoryInfo();
                if (!memInfo) return;

                const now = Date.now();

                if (memInfo.percentage > 90 && onCritical) {
                    // 临界状态，立即通知
                    onCritical(memInfo);
                } else if (memInfo.percentage > threshold && onWarning) {
                    // 警告状态，但避免频繁通知
                    if (now - lastWarningTime > 30000) { // 30秒内最多通知一次
                        onWarning(memInfo);
                        lastWarningTime = now;
                    }
                }
            }, interval);

            logger.debug('内存监控器已启动');
        },

        stop() {
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
                logger.debug('内存监控器已停止');
            }
        },

        getCurrentInfo() {
            return getMemoryInfo();
        }
    };
}