/**
 * @fileoverview 输入验证和清理器
 * @description 提供通用的输入验证和数据清理功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 验证规则接口
 */
export interface ValidationRule<T = any> {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: T) => boolean | string;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    sanitized?: any;
}

/**
 * 验证和清理数据
 */
export function validateAndSanitize(
    data: any,
    rules: Record<string, ValidationRule>,
    options: { strict?: boolean; sanitize?: boolean } = {}
): ValidationResult {
    const { strict = false, sanitize = true } = options;
    const result: ValidationResult = {
        valid: true,
        errors: [],
        sanitized: sanitize ? {} : undefined
    };

    // 检查必需字段
    for (const [key, rule] of Object.entries(rules)) {
        if (rule.required && (data[key] === undefined || data[key] === null)) {
            result.errors.push(`字段 '${key}' 是必需的`);
            result.valid = false;
            continue;
        }

        const value = data[key];
        if (value === undefined || value === null) {
            continue;
        }

        // 类型检查
        if (rule.type) {
            const actualType = Array.isArray(value) ? 'array' : typeof value;
            if (actualType !== rule.type) {
                result.errors.push(`字段 '${key}' 应该是 ${rule.type} 类型，实际是 ${actualType}`);
                result.valid = false;
                if (strict) continue;
            }
        }

        // 字符串验证
        if (rule.type === 'string' && typeof value === 'string') {
            if (rule.minLength && value.length < rule.minLength) {
                result.errors.push(`字段 '${key}' 长度不能少于 ${rule.minLength} 个字符`);
                result.valid = false;
            }
            if (rule.maxLength && value.length > rule.maxLength) {
                result.errors.push(`字段 '${key}' 长度不能超过 ${rule.maxLength} 个字符`);
                result.valid = false;
            }
            if (rule.pattern && !rule.pattern.test(value)) {
                result.errors.push(`字段 '${key}' 格式不正确`);
                result.valid = false;
            }
        }

        // 数字验证
        if (rule.type === 'number' && typeof value === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                result.errors.push(`字段 '${key}' 不能小于 ${rule.min}`);
                result.valid = false;
            }
            if (rule.max !== undefined && value > rule.max) {
                result.errors.push(`字段 '${key}' 不能大于 ${rule.max}`);
                result.valid = false;
            }
        }

        // 自定义验证
        if (rule.custom) {
            const customResult = rule.custom(value);
            if (customResult !== true) {
                const errorMessage = typeof customResult === 'string'
                    ? customResult
                    : `字段 '${key}' 验证失败`;
                result.errors.push(errorMessage);
                result.valid = false;
            }
        }

        // 数据清理
        if (sanitize && result.sanitized) {
            if (rule.type === 'string' && typeof value === 'string') {
                // 清理字符串：去除首尾空格，转义HTML
                result.sanitized[key] = value.trim().replace(/[<>]/g, '');
            } else {
                result.sanitized[key] = value;
            }
        }
    }

    return result;
}