/**
 * @fileoverview 浏览器兼容性检查器
 * @description 提供浏览器特性检测和兼容性验证功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 兼容性检查结果接口
 */
export interface CompatibilityResult {
    compatible: boolean;
    missing: string[];
    warnings: string[];
}

/**
 * 检查浏览器兼容性
 */
export function checkBrowserCompatibility(requirements?: {
    es6?: boolean;
    proxy?: boolean;
    customElements?: boolean;
    shadowDOM?: boolean;
    webComponents?: boolean;
    fetch?: boolean;
    promise?: boolean;
}): CompatibilityResult {
    const result: CompatibilityResult = {
        compatible: true,
        missing: [],
        warnings: []
    };

    const defaultRequirements = {
        es6: true,
        proxy: true,
        customElements: false,
        shadowDOM: false,
        webComponents: false,
        fetch: true,
        promise: true,
        ...requirements
    };

    // 检查 ES6 支持
    if (defaultRequirements.es6) {
        try {
            new Function('(a = 0) => a');
        } catch {
            result.missing.push('ES6 Arrow Functions');
            result.compatible = false;
        }
    }

    // 检查 Proxy 支持
    if (defaultRequirements.proxy && typeof Proxy === 'undefined') {
        result.missing.push('Proxy');
        result.compatible = false;
    }

    // 检查 Custom Elements 支持
    if (defaultRequirements.customElements && typeof customElements === 'undefined') {
        result.missing.push('Custom Elements');
        result.compatible = false;
    }

    // 检查 Shadow DOM 支持
    if (defaultRequirements.shadowDOM && typeof ShadowRoot === 'undefined') {
        result.missing.push('Shadow DOM');
        result.compatible = false;
    }

    // 检查 Fetch 支持
    if (defaultRequirements.fetch && typeof fetch === 'undefined') {
        result.missing.push('Fetch API');
        result.compatible = false;
    }

    // 检查 Promise 支持
    if (defaultRequirements.promise && typeof Promise === 'undefined') {
        result.missing.push('Promise');
        result.compatible = false;
    }

    return result;
}

/**
 * 环境特性接口
 */
export interface EnvironmentFeatures {
    // 基础特性
    es6: boolean;
    es2017: boolean;
    es2020: boolean;

    // Web APIs
    fetch: boolean;
    webWorkers: boolean;
    serviceWorkers: boolean;
    webAssembly: boolean;

    // 存储
    localStorage: boolean;
    sessionStorage: boolean;
    indexedDB: boolean;

    // 网络
    onlineStatus: boolean;
    connectionInfo: boolean;

    // 设备
    deviceMemory: boolean;
    hardwareConcurrency: boolean;

    // 安全
    crypto: boolean;
    secureContext: boolean;
}

/**
 * 检测环境特性
 */
export function detectEnvironmentFeatures(): EnvironmentFeatures {
    const features: EnvironmentFeatures = {
        // 基础特性检测
        es6: (() => {
            try {
                new Function('(a = 0) => a');
                return true;
            } catch {
                return false;
            }
        })(),

        es2017: (() => {
            try {
                new Function('async () => {}');
                return true;
            } catch {
                return false;
            }
        })(),

        es2020: (() => {
            try {
                new Function('a?.b');
                return true;
            } catch {
                return false;
            }
        })(),

        // Web APIs
        fetch: typeof fetch !== 'undefined',
        webWorkers: typeof Worker !== 'undefined',
        serviceWorkers: 'serviceWorker' in navigator,
        webAssembly: typeof WebAssembly !== 'undefined',

        // 存储
        localStorage: (() => {
            try {
                return typeof localStorage !== 'undefined' && localStorage !== null;
            } catch {
                return false;
            }
        })(),

        sessionStorage: (() => {
            try {
                return typeof sessionStorage !== 'undefined' && sessionStorage !== null;
            } catch {
                return false;
            }
        })(),

        indexedDB: 'indexedDB' in window,

        // 网络
        onlineStatus: 'onLine' in navigator,
        connectionInfo: 'connection' in navigator,

        // 设备
        deviceMemory: 'deviceMemory' in navigator,
        hardwareConcurrency: 'hardwareConcurrency' in navigator,

        // 安全
        crypto: typeof window !== 'undefined' && 'crypto' in window && 'subtle' in crypto,
        secureContext: typeof window !== 'undefined' && 'isSecureContext' in window ? window.isSecureContext :
            (typeof location !== 'undefined' ? location.protocol === 'https:' : false)
    };

    return features;
}