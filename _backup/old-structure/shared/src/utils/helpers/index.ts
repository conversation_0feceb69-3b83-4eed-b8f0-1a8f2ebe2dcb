/**
 * @fileoverview 共享辅助函数 - 高级业务逻辑辅助工具
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

// 验证相关
export * from './validation/app-config';
export * from './validation/input-validator';

// 性能相关
export * from './performance/memory-monitor';
export * from './performance/timer';

// 兼容性检查
export * from './compatibility/browser-check';

// 资源加载
export * from './resource/loader';

// 工具函数
export * from './utils/error-formatter';

// 重新导出主要函数
export { checkBrowserCompatibility, detectEnvironmentFeatures } from './compatibility/browser-check';
export { createMemoryMonitor, getMemoryInfo } from './performance/memory-monitor';
export { createPerformanceTimer } from './performance/timer';
export { loadResource } from './resource/loader';
export { formatErrorMessage } from './utils/error-formatter';
export { normalizeAppConfig, validateAppConfig } from './validation/app-config';
export { validateAndSanitize } from './validation/input-validator';

/**
 * 辅助函数工具集合
 */
export const helpers = {
    // 验证相关
    validateAppConfig,
    normalizeAppConfig,
    validateAndSanitize,

    // 性能相关
    createPerformanceTimer,
    getMemoryInfo,
    createMemoryMonitor,

    // 兼容性检查
    checkBrowserCompatibility,
    detectEnvironmentFeatures,

    // 资源加载
    loadResource,

    // 工具函数
    formatErrorMessage
};

// 默认导出
export default helpers;