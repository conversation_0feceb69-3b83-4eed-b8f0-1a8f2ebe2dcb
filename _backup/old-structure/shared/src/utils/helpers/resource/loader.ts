/**
 * @fileoverview 资源加载器
 * @description 提供统一的资源加载功能，支持脚本、样式、数据等多种资源类型
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import { createLogger } from '../../../utils/src/logger';

const logger = createLogger('micro-core:resource-loader');

/**
 * 资源加载选项接口
 */
export interface ResourceLoadOptions {
    timeout?: number;
    retries?: number;
    cache?: boolean;
    integrity?: string;
    crossOrigin?: string;
}

/**
 * 加载资源
 */
export async function loadResource(
    url: string,
    type: 'script' | 'style' | 'json' | 'text' = 'script',
    options: ResourceLoadOptions = {}
): Promise<any> {
    const {
        timeout = 30000,
        retries = 3,
        cache = true,
        integrity,
        crossOrigin
    } = options;

    const cacheKey = `resource:${url}`;

    // 检查缓存
    if (cache && typeof sessionStorage !== 'undefined') {
        try {
            const cached = sessionStorage.getItem(cacheKey);
            if (cached) {
                logger.debug(`从缓存加载资源: ${url}`);
                return JSON.parse(cached);
            }
        } catch (error) {
            logger.warn(`缓存读取失败: ${url}`, error);
        }
    }

    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            logger.debug(`加载资源 (尝试 ${attempt}/${retries}): ${url}`);

            let result: any;

            switch (type) {
                case 'script':
                    result = await loadScriptResource(url, { timeout, integrity, crossOrigin });
                    break;
                case 'style':
                    result = await loadStyleResource(url, { timeout, integrity, crossOrigin });
                    break;
                case 'json':
                case 'text':
                    result = await loadDataResource(url, type, { timeout });
                    break;
                default:
                    throw new Error(`不支持的资源类型: ${type}`);
            }

            // 缓存结果
            if (cache && typeof sessionStorage !== 'undefined') {
                try {
                    sessionStorage.setItem(cacheKey, JSON.stringify(result));
                } catch (error) {
                    logger.warn(`缓存写入失败: ${url}`, error);
                }
            }

            logger.debug(`资源加载成功: ${url}`);
            return result;

        } catch (error) {
            lastError = error as Error;
            logger.warn(`资源加载失败 (尝试 ${attempt}/${retries}): ${url}`, error);

            if (attempt < retries) {
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    throw new Error(`资源加载失败，已重试 ${retries} 次: ${url}. 最后错误: ${lastError?.message || '未知错误'}`);
}

/**
 * 加载脚本资源
 */
async function loadScriptResource(
    url: string,
    options: { timeout: number; integrity?: string; crossOrigin?: string }
): Promise<void> {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = url;
        script.async = true;

        if (options.integrity) {
            script.integrity = options.integrity;
        }

        if (options.crossOrigin) {
            script.crossOrigin = options.crossOrigin;
        }

        const timeoutId = setTimeout(() => {
            script.remove();
            reject(new Error(`脚本加载超时: ${url}`));
        }, options.timeout);

        script.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        script.onerror = () => {
            clearTimeout(timeoutId);
            script.remove();
            reject(new Error(`脚本加载失败: ${url}`));
        };

        document.head.appendChild(script);
    });
}

/**
 * 加载样式资源
 */
async function loadStyleResource(
    url: string,
    options: { timeout: number; integrity?: string; crossOrigin?: string }
): Promise<void> {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = url;

        if (options.integrity) {
            link.integrity = options.integrity;
        }

        if (options.crossOrigin) {
            link.crossOrigin = options.crossOrigin;
        }

        const timeoutId = setTimeout(() => {
            link.remove();
            reject(new Error(`样式加载超时: ${url}`));
        }, options.timeout);

        link.onload = () => {
            clearTimeout(timeoutId);
            resolve();
        };

        link.onerror = () => {
            clearTimeout(timeoutId);
            link.remove();
            reject(new Error(`样式加载失败: ${url}`));
        };

        document.head.appendChild(link);
    });
}

/**
 * 加载数据资源
 */
async function loadDataResource(
    url: string,
    type: 'json' | 'text',
    options: { timeout: number }
): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout);

    try {
        const response = await fetch(url, {
            signal: controller.signal,
            headers: {
                'Accept': type === 'json' ? 'application/json' : 'text/plain'
            }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return type === 'json' ? await response.json() : await response.text();
    } catch (error) {
        clearTimeout(timeoutId);
        if (error instanceof Error && error.name === 'AbortError') {
            throw new Error(`数据加载超时: ${url}`);
        }
        throw error;
    }
}