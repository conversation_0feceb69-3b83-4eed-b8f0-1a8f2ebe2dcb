/**
 * @fileoverview 错误信息格式化器
 * @description 提供统一的错误信息格式化功能
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

/**
 * 错误信息格式化器
 */
export function formatErrorMessage(error: Error | string, context?: Record<string, any>): string {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'object' && error.stack ? error.stack : '';

    let formatted = `错误: ${errorMessage}`;

    if (context) {
        const contextStr = Object.entries(context)
            .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
            .join(', ');
        formatted += ` | 上下文: ${contextStr}`;
    }

    if (errorStack && process.env.NODE_ENV === 'development') {
        formatted += `\n堆栈: ${errorStack}`;
    }

    return formatted;
}