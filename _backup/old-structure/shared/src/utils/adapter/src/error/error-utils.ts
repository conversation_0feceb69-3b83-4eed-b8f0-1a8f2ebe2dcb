/**
 * @fileoverview 错误处理工具函数
 * @description 提供错误格式化、处理和报告的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 错误类型枚举
 */
export enum ErrorType {
    MOUNT_ERROR = 'mount_error',
    UNMOUNT_ERROR = 'unmount_error',
    LOAD_ERROR = 'load_error',
    RUNTIME_ERROR = 'runtime_error',
    CONFIG_ERROR = 'config_error',
    NETWORK_ERROR = 'network_error',
    VALIDATION_ERROR = 'validation_error',
    TIMEOUT_ERROR = 'timeout_error',
    UNKNOWN_ERROR = 'unknown_error'
}

/**
 * 错误严重级别
 */
export enum ErrorSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
    type: ErrorType;
    severity: ErrorSeverity;
    message: string;
    stack?: string;
    timestamp: number;
    context?: Record<string, any>;
    source?: string;
    appName?: string;
    userId?: string;
    sessionId?: string;
}

/**
 * 错误处理器接口
 */
export interface ErrorHandler {
    handle(error: Error, context?: Record<string, any>): void;
    canHandle(error: Error): boolean;
}

/**
 * 格式化错误信息
 */
export function formatError(
    error: Error,
    options: {
        type?: ErrorType;
        severity?: ErrorSeverity;
        context?: Record<string, any>;
        source?: string;
        appName?: string;
    } = {}
): ErrorInfo {
    return {
        type: options.type || getErrorType(error),
        severity: options.severity || getErrorSeverity(error),
        message: error.message || '未知错误',
        stack: error.stack,
        timestamp: Date.now(),
        context: options.context,
        source: options.source,
        appName: options.appName,
        userId: getCurrentUserId(),
        sessionId: getCurrentSessionId()
    };
}

/**
 * 获取错误类型
 */
function getErrorType(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    if (message.includes('mount') || message.includes('挂载')) {
        return ErrorType.MOUNT_ERROR;
    }
    if (message.includes('unmount') || message.includes('卸载')) {
        return ErrorType.UNMOUNT_ERROR;
    }
    if (message.includes('load') || message.includes('加载')) {
        return ErrorType.LOAD_ERROR;
    }
    if (message.includes('config') || message.includes('配置')) {
        return ErrorType.CONFIG_ERROR;
    }
    if (message.includes('network') || message.includes('网络') || name.includes('fetch')) {
        return ErrorType.NETWORK_ERROR;
    }
    if (message.includes('validation') || message.includes('验证')) {
        return ErrorType.VALIDATION_ERROR;
    }
    if (message.includes('timeout') || message.includes('超时')) {
        return ErrorType.TIMEOUT_ERROR;
    }

    return ErrorType.RUNTIME_ERROR;
}

/**
 * 获取错误严重级别
 */
function getErrorSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();

    if (message.includes('critical') || message.includes('fatal') || message.includes('严重')) {
        return ErrorSeverity.CRITICAL;
    }
    if (message.includes('warning') || message.includes('warn') || message.includes('警告')) {
        return ErrorSeverity.LOW;
    }
    if (message.includes('timeout') || message.includes('network') || message.includes('load')) {
        return ErrorSeverity.MEDIUM;
    }

    return ErrorSeverity.HIGH;
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId(): string | undefined {
    // 这里可以根据实际情况获取用户ID
    if (typeof window !== 'undefined') {
        return (window as any).__MICRO_CORE_USER_ID__ ||
            localStorage.getItem('userId') ||
            sessionStorage.getItem('userId');
    }
    return undefined;
}

/**
 * 获取当前会话ID
 */
function getCurrentSessionId(): string | undefined {
    // 这里可以根据实际情况获取会话ID
    if (typeof window !== 'undefined') {
        return (window as any).__MICRO_CORE_SESSION_ID__ ||
            sessionStorage.getItem('sessionId');
    }
    return undefined;
}

/**
 * 创建错误信息
 */
export function createErrorInfo(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Record<string, any>
): ErrorInfo {
    return {
        type,
        severity,
        message,
        timestamp: Date.now(),
        context,
        userId: getCurrentUserId(),
        sessionId: getCurrentSessionId()
    };
}

/**
 * 包装错误
 */
export function wrapError(
    originalError: Error,
    message: string,
    context?: Record<string, any>
): Error {
    const wrappedError = new Error(message);
    wrappedError.stack = `${wrappedError.stack}\nCaused by: ${originalError.stack}`;
    (wrappedError as any).originalError = originalError;
    (wrappedError as any).context = context;
    return wrappedError;
}

/**
 * 安全执行函数
 */
export async function safeExecute<T>(
    fn: () => T | Promise<T>,
    options: {
        onError?: (error: Error) => void;
        defaultValue?: T;
        timeout?: number;
        retries?: number;
        retryDelay?: number;
    } = {}
): Promise<T | undefined> {
    const {
        onError,
        defaultValue,
        timeout,
        retries = 0,
        retryDelay = 1000
    } = options;

    let lastError: Error | undefined;

    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            let result: T | Promise<T>;

            if (timeout) {
                result = await Promise.race([
                    Promise.resolve(fn()),
                    new Promise<never>((_, reject) => {
                        setTimeout(() => reject(new Error(`操作超时 (${timeout}ms)`)), timeout);
                    })
                ]);
            } else {
                result = await Promise.resolve(fn());
            }

            return result;
        } catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));

            if (attempt < retries) {
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                continue;
            }

            if (onError) {
                onError(lastError);
            }

            if (defaultValue !== undefined) {
                return defaultValue;
            }

            break;
        }
    }

    return undefined;
}

/**
 * 错误边界装饰器
 */
export function errorBoundary(options: {
    onError?: (error: Error, context?: any) => void;
    fallback?: any;
    rethrow?: boolean;
} = {}) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = async function (...args: any[]) {
            try {
                const result = await originalMethod.apply(this, args);
                return result;
            } catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));

                if (options.onError) {
                    options.onError(err, { target, propertyKey, args });
                }

                if (options.rethrow !== false) {
                    throw err;
                }

                return options.fallback;
            }
        };

        return descriptor;
    };
}

/**
 * 创建错误处理器
 */
export function createErrorHandler(options: {
    onError?: (errorInfo: ErrorInfo) => void;
    logToConsole?: boolean;
    reportToServer?: boolean;
    serverEndpoint?: string;
    maxRetries?: number;
} = {}): ErrorHandler {
    const {
        onError,
        logToConsole = true,
        reportToServer = false,
        serverEndpoint,
        maxRetries = 3
    } = options;

    return {
        canHandle(): boolean {
            return true; // 默认处理所有错误
        },

        handle(error: Error, context?: Record<string, any>): void {
            const errorInfo = formatError(error, { context });

            // 控制台日志
            if (logToConsole) {
                console.error('错误处理器捕获到错误:', errorInfo);
            }

            // 自定义处理
            if (onError) {
                try {
                    onError(errorInfo);
                } catch (handlerError) {
                    console.error('错误处理器执行失败:', handlerError);
                }
            }

            // 服务器报告
            if (reportToServer && serverEndpoint) {
                reportToServerInternal(errorInfo, serverEndpoint, maxRetries);
            }
        }
    };

    async function reportToServerInternal(
        errorInfo: ErrorInfo,
        endpoint: string,
        retries: number
    ): Promise<void> {
        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(errorInfo)
                });
                break;
            } catch (reportError) {
                if (attempt === retries - 1) {
                    console.error('错误报告失败:', reportError);
                }
            }
        }
    }
};
}

/**
 * 全局错误处理器
 */
class GlobalErrorHandler {
    private handlers: ErrorHandler[] = [];
    private isSetup = false;

    addHandler(handler: ErrorHandler): void {
        this.handlers.push(handler);
        this.setupGlobalHandlers();
    }

    removeHandler(handler: ErrorHandler): void {
        const index = this.handlers.indexOf(handler);
        if (index > -1) {
            this.handlers.splice(index, 1);
        }
    }

    handleError(error: Error, context?: Record<string, any>): void {
        for (const handler of this.handlers) {
            if (handler.canHandle(error)) {
                try {
                    handler.handle(error, context);
                } catch (handlerError) {
                    console.error('错误处理器执行失败:', handlerError);
                }
            }
        }
    }

    private setupGlobalHandlers(): void {
        if (this.isSetup || typeof window === 'undefined') {
            return;
        }

        this.isSetup = true;

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            const error = event.reason instanceof Error
                ? event.reason
                : new Error(String(event.reason));

            this.handleError(error, { type: 'unhandledrejection' });
        });

        // 捕获全局错误
        window.addEventListener('error', (event) => {
            const error = event.error instanceof Error
                ? event.error
                : new Error(event.message);

            this.handleError(error, {
                type: 'global_error',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });
    }
}

// 导出全局错误处理器实例
export const globalErrorHandler = new GlobalErrorHandler();

/**
 * 设置全局错误处理
 */
export function setupGlobalErrorHandling(options: {
    onError?: (errorInfo: ErrorInfo) => void;
    logToConsole?: boolean;
    reportToServer?: boolean;
    serverEndpoint?: string;
} = {}): void {
    const handler = createErrorHandler(options);
    globalErrorHandler.addHandler(handler);
}

/**
 * 错误重试工具
 */
export function withRetry<T>(
    fn: () => Promise<T>,
    options: {
        maxRetries?: number;
        delay?: number;
        backoff?: boolean;
        shouldRetry?: (error: Error) => boolean;
    } = {}
): Promise<T> {
    const {
        maxRetries = 3,
        delay = 1000,
        backoff = false,
        shouldRetry = () => true
    } = options;

    return new Promise(async (resolve, reject) => {
        let lastError: Error;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const result = await fn();
                resolve(result);
                return;
            } catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));

                if (attempt === maxRetries || !shouldRetry(lastError)) {
                    reject(lastError);
                    return;
                }

                const currentDelay = backoff ? delay * Math.pow(2, attempt) : delay;
                await new Promise(resolve => setTimeout(resolve, currentDelay));
            }
        }
    });
}