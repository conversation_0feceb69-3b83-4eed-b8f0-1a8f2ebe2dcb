/**
 * @fileoverview 容器管理工具函数
 * @description 提供容器创建、管理和清理的通用工具函数
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 */

/**
 * 容器配置接口
 */
export interface ContainerConfig {
    id?: string;
    className?: string;
    style?: Partial<CSSStyleDeclaration>;
    attributes?: Record<string, string>;
    innerHTML?: string;
    parent?: Element | string;
    tagName?: string;
}

/**
 * 容器信息接口
 */
export interface ContainerInfo {
    element: Element;
    id: string;
    created: number;
    config: ContainerConfig;
    mounted: boolean;
    children: Element[];
}

/**
 * 创建容器元素
 */
export function createContainer(config: ContainerConfig = {}): Element {
    const {
        id = generateContainerId(),
        className,
        style = {},
        attributes = {},
        innerHTML = '',
        tagName = 'div'
    } = config;

    // 创建元素
    const element = document.createElement(tagName);

    // 设置ID
    element.id = id;

    // 设置类名
    if (className) {
        element.className = className;
    }

    // 设置样式
    Object.entries(style).forEach(([property, value]) => {
        if (value !== undefined) {
            (element.style as any)[property] = value;
        }
    });

    // 设置属性
    Object.entries(attributes).forEach(([name, value]) => {
        element.setAttribute(name, value);
    });

    // 设置内容
    if (innerHTML) {
        element.innerHTML = innerHTML;
    }

    return element;
}

/**
 * 生成容器ID
 */
function generateContainerId(): string {
    return `micro-container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 挂载容器到父元素
 */
export function mountContainer(
    container: Element,
    parent: Element | string = document.body
): void {
    const parentElement = typeof parent === 'string'
        ? document.querySelector(parent)
        : parent;

    if (!parentElement) {
        throw new Error(`找不到父元素: ${parent}`);
    }

    if (!parentElement.contains(container)) {
        parentElement.appendChild(container);
    }
}

/**
 * 卸载容器
 */
export function unmountContainer(container: Element): void {
    if (container.parentNode) {
        container.parentNode.removeChild(container);
    }
}

/**
 * 清理容器
 */
export function cleanupContainer(container: Element): void {
    // 清理事件监听器
    const clone = container.cloneNode(true);
    if (container.parentNode) {
        container.parentNode.replaceChild(clone, container);
    }

    // 清空内容
    (clone as Element).innerHTML = '';
}

/**
 * 查找容器
 */
export function findContainer(selector: string): Element | null {
    return document.querySelector(selector);
}

/**
 * 查找所有容器
 */
export function findAllContainers(selector: string): Element[] {
    return Array.from(document.querySelectorAll(selector));
}

/**
 * 检查容器是否存在
 */
export function containerExists(selector: string): boolean {
    return findContainer(selector) !== null;
}

/**
 * 获取容器信息
 */
export function getContainerInfo(container: Element): ContainerInfo {
    return {
        element: container,
        id: container.id || '',
        created: parseInt(container.getAttribute('data-created') || '0', 10) || Date.now(),
        config: extractContainerConfig(container),
        mounted: document.body.contains(container),
        children: Array.from(container.children)
    };
}

/**
 * 提取容器配置
 */
function extractContainerConfig(container: Element): ContainerConfig {
    const config: ContainerConfig = {
        id: container.id,
        className: container.className,
        tagName: container.tagName.toLowerCase()
    };

    // 提取样式
    const computedStyle = window.getComputedStyle(container);
    config.style = {};
    for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle[i];
        (config.style as any)[property] = computedStyle.getPropertyValue(property);
    }

    // 提取属性
    config.attributes = {};
    for (let i = 0; i < container.attributes.length; i++) {
        const attr = container.attributes[i];
        config.attributes[attr.name] = attr.value;
    }

    return config;
}

/**
 * 验证容器配置
 */
export function validateContainerConfig(config: ContainerConfig): {
    valid: boolean;
    errors: string[];
    warnings: string[];
} {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查标签名
    if (config.tagName && !/^[a-zA-Z][a-zA-Z0-9-]*$/.test(config.tagName)) {
        errors.push('无效的标签名');
    }

    // 检查ID
    if (config.id && !/^[a-zA-Z][a-zA-Z0-9-_]*$/.test(config.id)) {
        errors.push('无效的ID格式');
    }

    // 检查父元素
    if (config.parent) {
        if (typeof config.parent === 'string') {
            if (typeof document !== 'undefined' && !document.querySelector(config.parent)) {
                warnings.push(`找不到父元素: ${config.parent}`);
            }
        } else if (!(config.parent instanceof Element)) {
            errors.push('父元素必须是有效的DOM元素或选择器');
        }
    }

    return {
        valid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 创建容器管理器
 */
export function createContainerManager() {
    const containers = new Map<string, ContainerInfo>();

    return {
        /**
         * 创建并管理容器
         */
        create(config: ContainerConfig = {}): Element {
            const container = createContainer(config);
            const info: ContainerInfo = {
                element: container,
                id: container.id,
                created: Date.now(),
                config,
                mounted: false,
                children: []
            };

            containers.set(container.id, info);

            // 设置创建时间属性
            container.setAttribute('data-created', info.created.toString());

            return container;
        },

        /**
         * 挂载容器
         */
        mount(containerId: string, parent?: Element | string): void {
            const info = containers.get(containerId);
            if (!info) {
                throw new Error(`找不到容器: ${containerId}`);
            }

            mountContainer(info.element, parent);
            info.mounted = true;
        },

        /**
         * 卸载容器
         */
        unmount(containerId: string): void {
            const info = containers.get(containerId);
            if (!info) {
                throw new Error(`找不到容器: ${containerId}`);
            }

            unmountContainer(info.element);
            info.mounted = false;
        },

        /**
         * 清理容器
         */
        cleanup(containerId: string): void {
            const info = containers.get(containerId);
            if (!info) {
                throw new Error(`找不到容器: ${containerId}`);
            }

            cleanupContainer(info.element);
            containers.delete(containerId);
        },

        /**
         * 获取容器
         */
        get(containerId: string): Element | undefined {
            const info = containers.get(containerId);
            return info?.element;
        },

        /**
         * 获取容器信息
         */
        getInfo(containerId: string): ContainerInfo | undefined {
            return containers.get(containerId);
        },

        /**
         * 获取所有容器
         */
        getAll(): ContainerInfo[] {
            return Array.from(containers.values());
        },

        /**
         * 检查容器是否存在
         */
        has(containerId: string): boolean {
            return containers.has(containerId);
        },

        /**
         * 清理所有容器
         */
        cleanupAll(): void {
            const containerIds = Array.from(containers.keys());
            containerIds.forEach(id => {
                this.cleanup(id);
            });
        },

        /**
         * 获取容器数量
         */
        size(): number {
            return containers.size;
        }
    };
}

/**
 * 容器样式工具
 */
export const ContainerStyles = {
    /**
     * 隐藏容器
     */
    hide(container: Element): void {
        (container as HTMLElement).style.display = 'none';
    },

    /**
     * 显示容器
     */
    show(container: Element, display: string = 'block'): void {
        (container as HTMLElement).style.display = display;
    },

    /**
     * 设置容器尺寸
     */
    setSize(container: Element, width: string | number, height: string | number): void {
        const element = container as HTMLElement;
        element.style.width = typeof width === 'number' ? `${width}px` : width;
        element.style.height = typeof height === 'number' ? `${height}px` : height;
    },

    /**
     * 设置容器位置
     */
    setPosition(container: Element, position: {
        top?: string | number;
        left?: string | number;
        right?: string | number;
        bottom?: string | number;
        position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';
    }): void {
        const element = container as HTMLElement;

        if (position.position) {
            element.style.position = position.position;
        }

        ['top', 'left', 'right', 'bottom'].forEach(prop => {
            const value = (position as any)[prop];
            if (value !== undefined) {
                (element.style as any)[prop] = typeof value === 'number' ? `${value}px` : value;
            }
        });
    },

    /**
     * 设置容器边距
     */
    setMargin(container: Element, margin: string | number | {
        top?: string | number;
        right?: string | number;
        bottom?: string | number;
        left?: string | number;
    }): void {
        const element = container as HTMLElement;

        if (typeof margin === 'string' || typeof margin === 'number') {
            element.style.margin = typeof margin === 'number' ? `${margin}px` : margin;
        } else {
            ['top', 'right', 'bottom', 'left'].forEach(side => {
                const value = (margin as any)[side];
                if (value !== undefined) {
                    (element.style as any)[`margin${side.charAt(0).toUpperCase() + side.slice(1)}`] =
                        typeof value === 'number' ? `${value}px` : value;
                }
            });
        }
    },

    /**
     * 设置容器内边距
     */
    setPadding(container: Element, padding: string | number | {
        top?: string | number;
        right?: string | number;
        bottom?: string | number;
        left?: string | number;
    }): void {
        const element = container as HTMLElement;

        if (typeof padding === 'string' || typeof padding === 'number') {
            element.style.padding = typeof padding === 'number' ? `${padding}px` : padding;
        } else {
            ['top', 'right', 'bottom', 'left'].forEach(side => {
                const value = (padding as any)[side];
                if (value !== undefined) {
                    (element.style as any)[`padding${side.charAt(0).toUpperCase() + side.slice(1)}`] =
                        typeof value === 'number' ? `${value}px` : value;
                }
            });
        }
    },

    /**
     * 设置容器边框
     */
    setBorder(container: Element, border: string | {
        width?: string | number;
        style?: string;
        color?: string;
    }): void {
        const element = container as HTMLElement;

        if (typeof border === 'string') {
            element.style.border = border;
        } else {
            if (border.width !== undefined) {
                element.style.borderWidth = typeof border.width === 'number' ? `${border.width}px` : border.width;
            }
            if (border.style) {
                element.style.borderStyle = border.style;
            }
            if (border.color) {
                element.style.borderColor = border.color;
            }
        }
    },

    /**
     * 设置容器背景
     */
    setBackground(container: Element, background: string | {
        color?: string;
        image?: string;
        repeat?: string;
        position?: string;
        size?: string;
    }): void {
        const element = container as HTMLElement;

        if (typeof background === 'string') {
            element.style.background = background;
        } else {
            if (background.color) {
                element.style.backgroundColor = background.color;
            }
            if (background.image) {
                element.style.backgroundImage = background.image;
            }
            if (background.repeat) {
                element.style.backgroundRepeat = background.repeat;
            }
            if (background.position) {
                element.style.backgroundPosition = background.position;
            }
            if (background.size) {
                element.style.backgroundSize = background.size;
            }
        }
    }
};

// 导出全局容器管理器实例
export const globalContainerManager = createContainerManager();