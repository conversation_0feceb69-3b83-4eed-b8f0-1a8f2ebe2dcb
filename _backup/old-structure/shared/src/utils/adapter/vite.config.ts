import { resolve } from 'path';
import { defineConfig } from 'vite';

export default defineConfig({
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),
            name: 'MicroCoreSharedAdapterUtils',
            formats: ['es', 'cjs'],
            fileName: (format) => `index.${format === 'es' ? 'esm' : format}.js`
        },
        rollupOptions: {
            external: ['@micro-core/shared-types'],
            output: {
                globals: {
                    '@micro-core/shared-types': 'MicroCoreSharedTypes'
                }
            }
        },
        sourcemap: true,
        minify: false
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src')
        }
    }
});