import { defineConfig } from 'tsup';

export default defineConfig({
    entry: {
        index: 'src/index.ts',
        webpack: 'src/webpack.ts',
        vite: 'src/vite.ts',
        rollup: 'src/rollup.ts',
        esbuild: 'src/esbuild.ts'
    },
    format: ['cjs', 'esm'],
    dts: true,
    clean: true,
    splitting: false,
    sourcemap: true,
    minify: false,
    target: 'node16',
    outDir: 'dist',
    external: [
        'webpack',
        'vite',
        'rollup',
        'esbuild',
        '@micro-core/shared',
        '@micro-core/core'
    ],
    banner: {
        js: '/* @micro-core/builders - 微前端构建工具适配器 */'
    }
});