import { defineConfig } from 'tsup';

export default defineConfig({
    entry: {
        index: 'src/index.ts',
        proxy: 'src/proxy.ts',
        bridge: 'src/bridge.ts',
        isolation: 'src/isolation.ts'
    },
    format: ['esm', 'cjs'],
    dts: true,
    clean: true,
    splitting: false,
    sourcemap: true,
    minify: false,
    target: 'es2020',
    outDir: 'dist',
    external: [
        '@micro-core/shared',
        '@micro-core/core'
    ],
    banner: {
        js: '/* @micro-core/sidecar - 微前端边车模式支持 */'
    }
});