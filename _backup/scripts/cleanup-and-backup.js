#!/usr/bin/env node

/**
 * @fileoverview 清理和备份脚本
 * @description 识别并移除冗余文件，将清理的文件按原始目录结构归档至_backup目录
 * <AUTHOR> <<EMAIL>>
 * @version 0.1.0
 */

import crypto from 'crypto';
import fs from 'fs';
import { glob } from 'glob';
import { minimatch } from 'minimatch';
import path from 'path';

/**
 * 备份目录
 */
const BACKUP_DIR = '_backup';

/**
 * 需要清理的文件模式
 */
const CLEANUP_PATTERNS = [
    // 重复的资源文件
    '**/duplicate-*',
    '**/*-copy.*',
    '**/*-backup.*',
    '**/*.bak',

    // 临时文件
    '**/*.tmp',
    '**/*.temp',
    '**/temp-*',

    // 编辑器临时文件
    '**/.DS_Store',
    '**/Thumbs.db',
    '**/*.swp',
    '**/*.swo',
    '**/*~',

    // 废弃的模块代码（根据项目特点定义）
    '**/legacy/**',
    '**/deprecated/**',
    '**/old/**',

    // 测试临时文件
    '**/test-output/**',
    '**/coverage-temp/**',

    // 构建临时文件
    '**/.turbo',
    '**/dist-temp/**',

    // 旧的tsup配置文件（统一使用vite）
    '**/tsup.config.ts',
    '**/tsup.config.js'
];

/**
 * 需要保留的重要文件（即使匹配清理模式）
 */
const PRESERVE_PATTERNS = [
    'node_modules/**',
    '.git/**',
    'dist/**',
    'coverage/**'
];

/**
 * 创建备份目录
 * @param {string} originalPath 原始文件路径
 * @returns {string} 备份文件路径
 */
function createBackupPath(originalPath) {
    const relativePath = path.relative(process.cwd(), originalPath);
    return path.join(process.cwd(), BACKUP_DIR, relativePath);
}

/**
 * 确保目录存在
 * @param {string} dirPath 目录路径
 */
function ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}

/**
 * 移动文件到备份目录
 * @param {string} filePath 文件路径
 */
function backupFile(filePath) {
    try {
        const backupPath = createBackupPath(filePath);
        const backupDir = path.dirname(backupPath);

        // 确保备份目录存在
        ensureDir(backupDir);

        // 移动文件
        fs.renameSync(filePath, backupPath);

        console.log(`📦 备份: ${path.relative(process.cwd(), filePath)} → ${path.relative(process.cwd(), backupPath)}`);
        return true;
    } catch (error) {
        console.error(`❌ 备份失败 ${filePath}:`, error.message);
        return false;
    }
}

/**
 * 检查文件是否应该被保留
 * @param {string} filePath 文件路径
 * @returns {boolean} 是否应该保留
 */
function shouldPreserve(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);

    return PRESERVE_PATTERNS.some(pattern => {
        return minimatch(relativePath, pattern);
    });
}

/**
 * 识别重复文件
 * @returns {Array} 重复文件列表
 */
function findDuplicateFiles() {
    const duplicates = [];
    const fileHashes = new Map();

    // 获取所有文件
    const allFiles = glob.sync('**/*', {
        cwd: process.cwd(),
        nodir: true,
        ignore: ['node_modules/**', '.git/**', 'dist/**', 'coverage/**', '_backup/**']
    });

    allFiles.forEach(filePath => {
        const fullPath = path.join(process.cwd(), filePath);

        try {
            const stats = fs.statSync(fullPath);
            if (stats.isFile() && stats.size > 0) {
                const content = fs.readFileSync(fullPath);
                const hash = crypto.createHash('md5').update(content).digest('hex');

                if (fileHashes.has(hash)) {
                    // 发现重复文件
                    const existingFile = fileHashes.get(hash);
                    duplicates.push({
                        original: existingFile,
                        duplicate: fullPath,
                        size: stats.size
                    });
                } else {
                    fileHashes.set(hash, fullPath);
                }
            }
        } catch (error) {
            // 忽略无法读取的文件
        }
    });

    return duplicates;
}

/**
 * 识别空目录
 * @returns {Array} 空目录列表
 */
function findEmptyDirectories() {
    const emptyDirs = [];

    const allDirs = glob.sync('**/', {
        cwd: process.cwd(),
        ignore: ['node_modules/**', '.git/**', 'dist/**', 'coverage/**', '_backup/**']
    });

    allDirs.forEach(dirPath => {
        const fullPath = path.join(process.cwd(), dirPath);

        try {
            const items = fs.readdirSync(fullPath);
            if (items.length === 0) {
                emptyDirs.push(fullPath);
            }
        } catch (error) {
            // 忽略无法读取的目录
        }
    });

    return emptyDirs;
}

/**
 * 分析项目中的废弃代码
 * @returns {Array} 废弃代码文件列表
 */
function findDeprecatedCode() {
    const deprecated = [];

    // 查找包含废弃标记的文件
    const sourceFiles = glob.sync('packages/*/src/**/*.{ts,tsx,js,jsx}', {
        cwd: process.cwd()
    });

    sourceFiles.forEach(filePath => {
        const fullPath = path.join(process.cwd(), filePath);

        try {
            const content = fs.readFileSync(fullPath, 'utf-8');

            // 检查废弃标记
            if (content.includes('@deprecated') ||
                content.includes('// TODO: remove') ||
                content.includes('// FIXME: legacy') ||
                content.includes('// DEPRECATED')) {
                deprecated.push(fullPath);
            }
        } catch (error) {
            // 忽略无法读取的文件
        }
    });

    return deprecated;
}

/**
 * 主清理函数
 */
async function main() {
    console.log('🧹 开始清理和备份冗余文件...\n');

    // 创建备份目录
    ensureDir(path.join(process.cwd(), BACKUP_DIR));

    let totalCleaned = 0;
    let totalBackedUp = 0;

    try {
        // 1. 按模式清理文件
        console.log('📋 按模式清理文件...');
        for (const pattern of CLEANUP_PATTERNS) {
            const files = glob.sync(pattern, {
                cwd: process.cwd(),
                ignore: PRESERVE_PATTERNS
            });

            files.forEach(filePath => {
                const fullPath = path.join(process.cwd(), filePath);

                if (fs.existsSync(fullPath) && !shouldPreserve(fullPath)) {
                    if (backupFile(fullPath)) {
                        totalBackedUp++;
                    }
                }
            });
        }

        // 2. 清理重复文件
        console.log('\n🔍 查找重复文件...');
        const duplicates = findDuplicateFiles();

        if (duplicates.length > 0) {
            console.log(`发现 ${duplicates.length} 组重复文件:`);

            duplicates.forEach(({ original, duplicate, size }) => {
                console.log(`  📄 ${path.relative(process.cwd(), duplicate)} (${size} bytes)`);
                console.log(`     与 ${path.relative(process.cwd(), original)} 重复`);

                if (!shouldPreserve(duplicate)) {
                    if (backupFile(duplicate)) {
                        totalBackedUp++;
                    }
                }
            });
        } else {
            console.log('✅ 未发现重复文件');
        }

        // 3. 清理废弃代码
        console.log('\n🗑️ 查找废弃代码...');
        const deprecatedFiles = findDeprecatedCode();

        if (deprecatedFiles.length > 0) {
            console.log(`发现 ${deprecatedFiles.length} 个包含废弃标记的文件:`);

            deprecatedFiles.forEach(filePath => {
                console.log(`  📝 ${path.relative(process.cwd(), filePath)}`);
                // 注意：废弃代码文件不自动删除，需要手动确认
            });

            console.log('⚠️  废弃代码文件需要手动审查和处理');
        } else {
            console.log('✅ 未发现废弃代码标记');
        }

        // 4. 清理空目录
        console.log('\n📁 清理空目录...');
        const emptyDirs = findEmptyDirectories();

        if (emptyDirs.length > 0) {
            emptyDirs.forEach(dirPath => {
                try {
                    fs.rmdirSync(dirPath);
                    console.log(`🗂️ 删除空目录: ${path.relative(process.cwd(), dirPath)}`);
                    totalCleaned++;
                } catch (error) {
                    console.error(`❌ 删除目录失败 ${dirPath}:`, error.message);
                }
            });
        } else {
            console.log('✅ 未发现空目录');
        }

        // 5. 生成清理报告
        console.log('\n📊 清理报告:');
        console.log(`- 备份文件数: ${totalBackedUp}`);
        console.log(`- 清理目录数: ${totalCleaned}`);
        console.log(`- 重复文件组: ${duplicates.length}`);
        console.log(`- 废弃代码文件: ${deprecatedFiles.length}`);
        console.log(`- 备份位置: ${BACKUP_DIR}/`);

        if (totalBackedUp > 0 || totalCleaned > 0) {
            console.log('\n🎉 清理完成！');
            console.log('\n💡 建议操作:');
            console.log('1. 检查备份文件确认无误');
            console.log('2. 运行测试确保功能正常');
            console.log('3. 如果一切正常，可以删除备份目录');
            console.log(`4. git add . && git commit -m "chore: cleanup redundant files"`);
        } else {
            console.log('\n✨ 项目已经很干净，无需清理！');
        }

    } catch (error) {
        console.error('❌ 清理过程中出错:', error.message);
        process.exit(1);
    }
}

// 执行主函数
main().catch(error => {
    console.error('❌ 执行失败:', error);
    process.exit(1);
});